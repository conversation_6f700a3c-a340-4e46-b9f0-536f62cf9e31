import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import * as XLSX from 'xlsx';

// Add Arabic font support with better handling
const addArabicFontSupport = (pdf: jsPDF) => {
  try {
    // Use Arial which has better Arabic support
    pdf.setFont('Arial', 'normal');
  } catch (error) {
    console.warn('Arabic font not available, using default font');
    pdf.setFont('helvetica', 'normal');
  }
};

// Enhanced PDF export with Arabic support and better chart rendering
export const exportComparisonReportToPdf = async (elementId: string, filename: string, title: string) => {
  const input = document.getElementById(elementId);
  if (!input) {
    console.error('Element not found:', elementId);
    throw new Error('Element not found');
  }

  try {
    // Add styles for better PDF rendering with Arabic support
    const style = document.createElement('style');
    style.id = 'pdf-export-styles';
    style.textContent = `
      .pdf-export-rtl {
        direction: rtl !important;
        text-align: right !important;
        font-family: 'Arial', 'Traditional Arabic', 'Amiri', sans-serif !important;
        background: white !important;
      }
      .pdf-export-rtl * {
        font-family: 'Arial', 'Traditional Arabic', 'Amiri', sans-serif !important;
      }
      .pdf-export-rtl .MuiDataGrid-root {
        direction: rtl !important;
      }
      .pdf-export-rtl .MuiDataGrid-columnHeaders {
        direction: rtl !important;
      }
      .pdf-export-rtl .MuiDataGrid-cell {
        text-align: right !important;
        direction: rtl !important;
      }
      .pdf-export-rtl table {
        direction: rtl !important;
        border-collapse: collapse !important;
        width: 100% !important;
      }
      .pdf-export-rtl th, .pdf-export-rtl td {
        text-align: right !important;
        border: 1px solid #ddd !important;
        padding: 8px !important;
        direction: rtl !important;
      }
      .pdf-export-rtl svg {
        background: white !important;
      }
      .pdf-export-rtl .recharts-wrapper {
        background: white !important;
      }
      .pdf-export-rtl .MuiPaper-root {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
      }
      .pdf-export-rtl .MuiCard-root {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
      }
    `;
    document.head.appendChild(style);

    // Add temporary class for RTL support
    input.classList.add('pdf-export-rtl');

    // Wait for any dynamic content to render
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Capture the element with enhanced options
    const canvas = await html2canvas(input, {
      scale: 2, // High quality
      useCORS: true,
      allowTaint: true,
      logging: false,
      backgroundColor: '#ffffff',
      removeContainer: false,
      imageTimeout: 20000,
      width: input.scrollWidth,
      height: input.scrollHeight,
      onclone: (clonedDoc) => {
        // Ensure all SVG elements are properly rendered
        const svgElements = clonedDoc.querySelectorAll('svg');
        svgElements.forEach(svg => {
          svg.style.background = 'white';
          svg.style.display = 'block';
        });

        // Fix DataGrid styling in cloned document
        const dataGrids = clonedDoc.querySelectorAll('.MuiDataGrid-root');
        dataGrids.forEach(grid => {
          grid.style.direction = 'rtl';
        });
      }
    });

    const imgData = canvas.toDataURL('image/png', 1.0);
    const pdf = new jsPDF('p', 'mm', 'a4');
    const imgWidth = 210; // A4 width in mm
    const pageHeight = 297; // A4 height in mm
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    let heightLeft = imgHeight;
    let position = 0;

    // Add Arabic font support
    addArabicFontSupport(pdf);

    // Add title with Arabic support
    pdf.setFontSize(16);
    pdf.text(title, imgWidth / 2, 15, { align: 'center' });
    position = 25; // Adjust position after title

    // Add image to PDF
    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
    heightLeft -= pageHeight - position;

    // Handle multiple pages
    while (heightLeft >= 0) {
      position = heightLeft - imgHeight;
      pdf.addPage();
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;
    }

    // Clean up
    input.classList.remove('pdf-export-rtl');
    const existingStyle = document.getElementById('pdf-export-styles');
    if (existingStyle) {
      document.head.removeChild(existingStyle);
    }

    // Save the PDF
    pdf.save(filename);
    return true;
  } catch (error) {
    console.error('Error generating PDF:', error);
    // Clean up on error
    input.classList.remove('pdf-export-rtl');
    const existingStyle = document.getElementById('pdf-export-styles');
    if (existingStyle) {
      document.head.removeChild(existingStyle);
    }
    throw error;
  }
};

// New function to export reports via backend (for better Arabic support)
export const exportReportToPdf = async (data: any[], title: string, filename: string) => {
  try {
    const response = await axios.post(
      'http://localhost:8000/generate-pdf-report',
      data,
      {
        params: { title },
        responseType: 'blob',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );

    // Create blob and download
    const blob = new Blob([response.data], { type: 'application/pdf' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    
    console.log('PDF exported successfully via backend');
  } catch (error) {
    console.error('Error exporting PDF via backend:', error);
    throw error;
  }
};

// Export chart as image for embedding in reports
export const exportChartAsImage = async (chartElementId: string): Promise<string> => {
  const chartElement = document.getElementById(chartElementId);
  if (!chartElement) {
    throw new Error('Chart element not found');
  }

  const canvas = await html2canvas(chartElement, {
    scale: 2,
    useCORS: true,
    allowTaint: true,
    backgroundColor: '#ffffff',
    logging: false,
  });

  return canvas.toDataURL('image/png', 1.0);
};

// Enhanced Excel export with Arabic support and formatting
export const exportComparisonReportToExcel = (
  comparisonData: any[],
  trendData: any[],
  kpiData: any,
  filename: string,
  title: string
) => {
  try {
    // Create a new workbook
    const wb = XLSX.utils.book_new();

    // Prepare comparison data with Arabic headers
    const comparisonHeaders = [
      'اسم الكيان',
      'الفئة',
      'اسم الدواء',
      'الكمية الإجمالية',
      'التكلفة الإجمالية',
      'عدد الحالات'
    ];

    const comparisonExcelData = [
      comparisonHeaders,
      ...comparisonData.map(item => [
        item.EntityName,
        item.CategoryName,
        item.DrugName,
        item.TotalQuantity,
        item.TotalCost,
        item.NumberOfCases
      ])
    ];

    // Create comparison worksheet
    const comparisonWs = XLSX.utils.aoa_to_sheet(comparisonExcelData);

    // Apply formatting to comparison sheet
    const comparisonRange = XLSX.utils.decode_range(comparisonWs['!ref'] || 'A1');

    // Set column widths
    comparisonWs['!cols'] = [
      { width: 20 }, // Entity Name
      { width: 15 }, // Category
      { width: 25 }, // Drug Name
      { width: 15 }, // Total Quantity
      { width: 15 }, // Total Cost
      { width: 15 }  // Number of Cases
    ];

    // Apply borders and formatting to all cells
    for (let row = comparisonRange.s.r; row <= comparisonRange.e.r; row++) {
      for (let col = comparisonRange.s.c; col <= comparisonRange.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
        if (!comparisonWs[cellAddress]) comparisonWs[cellAddress] = { t: 's', v: '' };

        comparisonWs[cellAddress].s = {
          border: {
            top: { style: 'thin', color: { rgb: '000000' } },
            bottom: { style: 'thin', color: { rgb: '000000' } },
            left: { style: 'thin', color: { rgb: '000000' } },
            right: { style: 'thin', color: { rgb: '000000' } }
          },
          alignment: { horizontal: 'right', vertical: 'center' },
          font: { name: 'Arial', sz: 11 }
        };

        // Header row formatting
        if (row === 0) {
          comparisonWs[cellAddress].s.fill = {
            fgColor: { rgb: 'E6E6FA' }
          };
          comparisonWs[cellAddress].s.font = {
            name: 'Arial',
            sz: 12,
            bold: true
          };
        }
      }
    }

    // Add comparison sheet to workbook
    XLSX.utils.book_append_sheet(wb, comparisonWs, 'بيانات المقارنة');

    // Prepare trend data if available
    if (trendData && trendData.length > 0) {
      const trendHeaders = [
        'الشهر',
        'الكمية الإجمالية',
        'التكلفة الإجمالية',
        'عدد الحالات'
      ];

      const trendExcelData = [
        trendHeaders,
        ...trendData.map(item => [
          item.Month,
          item.TotalQuantity,
          item.TotalCost,
          item.NumberOfCases
        ])
      ];

      const trendWs = XLSX.utils.aoa_to_sheet(trendExcelData);

      // Apply formatting to trend sheet
      const trendRange = XLSX.utils.decode_range(trendWs['!ref'] || 'A1');

      trendWs['!cols'] = [
        { width: 15 }, // Month
        { width: 18 }, // Total Quantity
        { width: 18 }, // Total Cost
        { width: 15 }  // Number of Cases
      ];

      // Apply borders and formatting
      for (let row = trendRange.s.r; row <= trendRange.e.r; row++) {
        for (let col = trendRange.s.c; col <= trendRange.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          if (!trendWs[cellAddress]) trendWs[cellAddress] = { t: 's', v: '' };

          trendWs[cellAddress].s = {
            border: {
              top: { style: 'thin', color: { rgb: '000000' } },
              bottom: { style: 'thin', color: { rgb: '000000' } },
              left: { style: 'thin', color: { rgb: '000000' } },
              right: { style: 'thin', color: { rgb: '000000' } }
            },
            alignment: { horizontal: 'right', vertical: 'center' },
            font: { name: 'Arial', sz: 11 }
          };

          if (row === 0) {
            trendWs[cellAddress].s.fill = {
              fgColor: { rgb: 'E6E6FA' }
            };
            trendWs[cellAddress].s.font = {
              name: 'Arial',
              sz: 12,
              bold: true
            };
          }
        }
      }

      XLSX.utils.book_append_sheet(wb, trendWs, 'الاتجاهات الشهرية');
    }

    // Create KPI summary sheet
    const kpiHeaders = ['المؤشر', 'القيمة'];
    const kpiExcelData = [
      kpiHeaders,
      ['التكلفة الإجمالية', kpiData.totalCost.toFixed(2)],
      ['الكمية الإجمالية', kpiData.totalQuantity.toString()],
      ['عدد الحالات', kpiData.totalCases.toString()],
      ['متوسط التكلفة لكل حالة', (kpiData.totalCases > 0 ? kpiData.totalCost / kpiData.totalCases : 0).toFixed(2)]
    ];

    const kpiWs = XLSX.utils.aoa_to_sheet(kpiExcelData);

    // Format KPI sheet
    const kpiRange = XLSX.utils.decode_range(kpiWs['!ref'] || 'A1');

    kpiWs['!cols'] = [
      { width: 25 }, // Indicator
      { width: 20 }  // Value
    ];

    for (let row = kpiRange.s.r; row <= kpiRange.e.r; row++) {
      for (let col = kpiRange.s.c; col <= kpiRange.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
        if (!kpiWs[cellAddress]) kpiWs[cellAddress] = { t: 's', v: '' };

        kpiWs[cellAddress].s = {
          border: {
            top: { style: 'thin', color: { rgb: '000000' } },
            bottom: { style: 'thin', color: { rgb: '000000' } },
            left: { style: 'thin', color: { rgb: '000000' } },
            right: { style: 'thin', color: { rgb: '000000' } }
          },
          alignment: { horizontal: 'right', vertical: 'center' },
          font: { name: 'Arial', sz: 11 }
        };

        if (row === 0) {
          kpiWs[cellAddress].s.fill = {
            fgColor: { rgb: 'E6E6FA' }
          };
          kpiWs[cellAddress].s.font = {
            name: 'Arial',
            sz: 12,
            bold: true
          };
        }
      }
    }

    XLSX.utils.book_append_sheet(wb, kpiWs, 'المؤشرات الرئيسية');

    // Write the file
    XLSX.writeFile(wb, filename);
    return true;
  } catch (error) {
    console.error('Error exporting to Excel:', error);
    throw error;
  }
};
