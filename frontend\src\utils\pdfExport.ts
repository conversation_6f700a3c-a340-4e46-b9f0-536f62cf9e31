import jsPD<PERSON> from 'jspdf';
import html2canvas from 'html2canvas';
import * as XLSX from 'xlsx';
import {
  PDF_CONFIG,
  EXCEL_CONFIG,
  ARABIC_HEADERS,
  CSS_STYLES,
  generateFilename,
  formatCurrency,
  ERROR_MESSAGES,
  type ExportData,
  type TrendData,
  type KPIData,
  type ExcelCellStyle
} from './exportConfig';

// Add Arabic font support with better handling
const addArabicFontSupport = (pdf: jsPDF) => {
  try {
    // Use Arial which has better Arabic support
    pdf.setFont(PDF_CONFIG.DEFAULT_FONT, 'normal');
  } catch (error) {
    console.warn('Arabic font not available, using default font');
    pdf.setFont(PDF_CONFIG.FALLBACK_FONT, 'normal');
  }
};

// Helper function to apply cell formatting
const applyCellFormatting = (worksheet: any, range: any) => {
  for (let row = range.s.r; row <= range.e.r; row++) {
    for (let col = range.s.c; col <= range.e.c; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
      if (!worksheet[cellAddress]) worksheet[cellAddress] = { t: 's', v: '' };

      const cellStyle: ExcelCellStyle = {
        border: {
          top: { style: 'thin', color: { rgb: EXCEL_CONFIG.BORDER_COLOR } },
          bottom: { style: 'thin', color: { rgb: EXCEL_CONFIG.BORDER_COLOR } },
          left: { style: 'thin', color: { rgb: EXCEL_CONFIG.BORDER_COLOR } },
          right: { style: 'thin', color: { rgb: EXCEL_CONFIG.BORDER_COLOR } }
        },
        alignment: { horizontal: 'right', vertical: 'center' },
        font: { name: EXCEL_CONFIG.DEFAULT_FONT, sz: EXCEL_CONFIG.CELL_FONT_SIZE }
      };

      // Header row formatting
      if (row === 0) {
        cellStyle.fill = {
          fgColor: { rgb: EXCEL_CONFIG.HEADER_BACKGROUND }
        };
        cellStyle.font = {
          name: EXCEL_CONFIG.DEFAULT_FONT,
          sz: EXCEL_CONFIG.HEADER_FONT_SIZE,
          bold: true
        };
      }

      worksheet[cellAddress].s = cellStyle;
    }
  }
};

// Enhanced PDF export with Arabic support and better chart rendering
export const exportComparisonReportToPdf = async (elementId: string, filename: string, title: string) => {
  const input = document.getElementById(elementId);
  if (!input) {
    console.error(ERROR_MESSAGES.ELEMENT_NOT_FOUND);
    throw new Error(ERROR_MESSAGES.ELEMENT_NOT_FOUND);
  }

  try {
    // Add styles for better PDF rendering with Arabic support
    const style = document.createElement('style');
    style.id = 'pdf-export-styles';
    style.textContent = CSS_STYLES.PDF_EXPORT;
    document.head.appendChild(style);

    // Add temporary class for RTL support
    input.classList.add('pdf-export-rtl');

    // Wait for any dynamic content to render
    await new Promise(resolve => setTimeout(resolve, PDF_CONFIG.RENDER_DELAY));

    // Capture the element with enhanced options
    const canvas = await html2canvas(input, {
      scale: PDF_CONFIG.SCALE,
      useCORS: true,
      allowTaint: true,
      logging: false,
      backgroundColor: '#ffffff',
      removeContainer: false,
      imageTimeout: PDF_CONFIG.IMAGE_TIMEOUT,
      width: input.scrollWidth,
      height: input.scrollHeight,
      onclone: (clonedDoc) => {
        // Ensure all SVG elements are properly rendered
        const svgElements = clonedDoc.querySelectorAll('svg');
        svgElements.forEach(svg => {
          svg.style.background = 'white';
          svg.style.display = 'block';
        });

        // Fix DataGrid styling in cloned document
        const dataGrids = clonedDoc.querySelectorAll('.MuiDataGrid-root');
        dataGrids.forEach(grid => {
          grid.style.direction = 'rtl';
        });
      }
    });

    const imgData = canvas.toDataURL('image/png', PDF_CONFIG.IMAGE_QUALITY);
    const pdf = new jsPDF('p', 'mm', 'a4');
    const imgWidth = PDF_CONFIG.PAGE_WIDTH;
    const pageHeight = PDF_CONFIG.PAGE_HEIGHT;
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    let heightLeft = imgHeight;
    let position = 0;

    // Add Arabic font support
    addArabicFontSupport(pdf);

    // Add title with Arabic support
    pdf.setFontSize(PDF_CONFIG.TITLE_FONT_SIZE);
    pdf.text(title, imgWidth / 2, 15, { align: 'center' });
    position = PDF_CONFIG.MARGIN_TOP;

    // Add image to PDF
    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
    heightLeft -= pageHeight - position;

    // Handle multiple pages
    while (heightLeft >= 0) {
      position = heightLeft - imgHeight;
      pdf.addPage();
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;
    }

    // Clean up
    input.classList.remove('pdf-export-rtl');
    const existingStyle = document.getElementById('pdf-export-styles');
    if (existingStyle) {
      document.head.removeChild(existingStyle);
    }

    // Save the PDF
    pdf.save(filename);
    return true;
  } catch (error) {
    console.error('Error generating PDF:', error);
    // Clean up on error
    input.classList.remove('pdf-export-rtl');
    const existingStyle = document.getElementById('pdf-export-styles');
    if (existingStyle) {
      document.head.removeChild(existingStyle);
    }
    throw error;
  }
};

// Removed old backend PDF export function - no longer needed

// Export chart as image for embedding in reports
export const exportChartAsImage = async (chartElementId: string): Promise<string> => {
  const chartElement = document.getElementById(chartElementId);
  if (!chartElement) {
    throw new Error('Chart element not found');
  }

  const canvas = await html2canvas(chartElement, {
    scale: 2,
    useCORS: true,
    allowTaint: true,
    backgroundColor: '#ffffff',
    logging: false,
  });

  return canvas.toDataURL('image/png', 1.0);
};

// Enhanced PDF export for drug reports
export const exportDrugReportToPdf = async (elementId: string, filename: string, title: string) => {
  const input = document.getElementById(elementId);
  if (!input) {
    console.error(ERROR_MESSAGES.ELEMENT_NOT_FOUND);
    throw new Error(ERROR_MESSAGES.ELEMENT_NOT_FOUND);
  }

  try {
    // Add styles for better PDF rendering with Arabic support
    const style = document.createElement('style');
    style.id = 'pdf-export-styles';
    style.textContent = CSS_STYLES.PDF_EXPORT;
    document.head.appendChild(style);

    // Add temporary class for RTL support
    input.classList.add('pdf-export-rtl');

    // Wait for any dynamic content to render
    await new Promise(resolve => setTimeout(resolve, PDF_CONFIG.RENDER_DELAY));

    // Capture the element with enhanced options
    const canvas = await html2canvas(input, {
      scale: PDF_CONFIG.SCALE,
      useCORS: true,
      allowTaint: true,
      logging: false,
      backgroundColor: '#ffffff',
      removeContainer: false,
      imageTimeout: PDF_CONFIG.IMAGE_TIMEOUT,
      width: input.scrollWidth,
      height: input.scrollHeight,
      onclone: (clonedDoc) => {
        // Ensure all tables are properly rendered
        const tables = clonedDoc.querySelectorAll('table');
        tables.forEach(table => {
          table.style.borderCollapse = 'collapse';
          table.style.direction = 'rtl';
        });

        // Fix table cells
        const cells = clonedDoc.querySelectorAll('td, th');
        cells.forEach(cell => {
          cell.style.border = '1px solid #ddd';
          cell.style.padding = '8px';
          cell.style.textAlign = 'right';
        });
      }
    });

    const imgData = canvas.toDataURL('image/png', PDF_CONFIG.IMAGE_QUALITY);
    const pdf = new jsPDF('p', 'mm', 'a4');
    const imgWidth = PDF_CONFIG.PAGE_WIDTH;
    const pageHeight = PDF_CONFIG.PAGE_HEIGHT;
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    let heightLeft = imgHeight;
    let position = 0;

    // Add Arabic font support
    addArabicFontSupport(pdf);

    // Add title with Arabic support
    pdf.setFontSize(PDF_CONFIG.TITLE_FONT_SIZE);
    pdf.text(title, imgWidth / 2, 15, { align: 'center' });
    position = PDF_CONFIG.MARGIN_TOP;

    // Add image to PDF
    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
    heightLeft -= pageHeight - position;

    // Handle multiple pages
    while (heightLeft >= 0) {
      position = heightLeft - imgHeight;
      pdf.addPage();
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;
    }

    // Clean up
    input.classList.remove('pdf-export-rtl');
    const existingStyle = document.getElementById('pdf-export-styles');
    if (existingStyle) {
      document.head.removeChild(existingStyle);
    }

    // Save the PDF
    pdf.save(filename);
    return true;
  } catch (error) {
    console.error('Error generating PDF:', error);
    // Clean up on error
    input.classList.remove('pdf-export-rtl');
    const existingStyle = document.getElementById('pdf-export-styles');
    if (existingStyle) {
      document.head.removeChild(existingStyle);
    }
    throw error;
  }
};

// Enhanced Excel export with Arabic support and formatting
export const exportComparisonReportToExcel = (
  comparisonData: ExportData[],
  trendData: TrendData[],
  kpiData: KPIData,
  filename: string,
  title: string
) => {
  try {
    // Create a new workbook
    const wb = XLSX.utils.book_new();

    // Prepare comparison data with Arabic headers
    const comparisonExcelData = [
      ARABIC_HEADERS.COMPARISON,
      ...comparisonData.map(item => [
        item.EntityName,
        item.CategoryName,
        item.DrugName,
        item.TotalQuantity,
        item.TotalCost,
        item.NumberOfCases
      ])
    ];

    // Create comparison worksheet
    const comparisonWs = XLSX.utils.aoa_to_sheet(comparisonExcelData);

    // Apply formatting to comparison sheet
    const comparisonRange = XLSX.utils.decode_range(comparisonWs['!ref'] || 'A1');

    // Set column widths using config
    comparisonWs['!cols'] = [
      { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.entityName },
      { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.category },
      { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.drugName },
      { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.quantity },
      { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.cost },
      { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.cases }
    ];

    // Apply borders and formatting to all cells
    applyCellFormatting(comparisonWs, comparisonRange);

    // Add comparison sheet to workbook
    XLSX.utils.book_append_sheet(wb, comparisonWs, EXCEL_CONFIG.SHEET_NAMES.comparison);

    // Prepare trend data if available
    if (trendData && trendData.length > 0) {
      const trendExcelData = [
        ARABIC_HEADERS.TRENDS,
        ...trendData.map(item => [
          item.Month,
          item.TotalQuantity,
          item.TotalCost,
          item.NumberOfCases
        ])
      ];

      const trendWs = XLSX.utils.aoa_to_sheet(trendExcelData);

      // Apply formatting to trend sheet
      const trendRange = XLSX.utils.decode_range(trendWs['!ref'] || 'A1');

      trendWs['!cols'] = [
        { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.month },
        { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.quantity },
        { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.cost },
        { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.cases }
      ];

      // Apply formatting using helper function
      applyCellFormatting(trendWs, trendRange);

      XLSX.utils.book_append_sheet(wb, trendWs, EXCEL_CONFIG.SHEET_NAMES.trends);
    }

    // Create KPI summary sheet
    const kpiExcelData = [
      ARABIC_HEADERS.KPI,
      [ARABIC_HEADERS.KPI_LABELS.totalCost, formatCurrency(kpiData.totalCost)],
      [ARABIC_HEADERS.KPI_LABELS.totalQuantity, kpiData.totalQuantity.toString()],
      [ARABIC_HEADERS.KPI_LABELS.totalCases, kpiData.totalCases.toString()],
      [ARABIC_HEADERS.KPI_LABELS.avgCostPerCase, formatCurrency(kpiData.totalCases > 0 ? kpiData.totalCost / kpiData.totalCases : 0)]
    ];

    const kpiWs = XLSX.utils.aoa_to_sheet(kpiExcelData);

    // Format KPI sheet
    const kpiRange = XLSX.utils.decode_range(kpiWs['!ref'] || 'A1');

    kpiWs['!cols'] = [
      { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.indicator },
      { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.value }
    ];

    // Apply formatting using helper function
    applyCellFormatting(kpiWs, kpiRange);

    XLSX.utils.book_append_sheet(wb, kpiWs, EXCEL_CONFIG.SHEET_NAMES.kpi);

    // Write the file
    XLSX.writeFile(wb, filename);
    return true;
  } catch (error) {
    console.error('Error exporting to Excel:', error);
    throw error;
  }
};

// Enhanced Excel export for drug reports
export const exportDrugReportToExcel = (
  clinicData: any[],
  regionData: any[],
  branchData: any[],
  filename: string,
  title: string,
  reportType: 'clinic' | 'region' | 'branch' | 'all'
) => {
  try {
    // Create a new workbook
    const wb = XLSX.utils.book_new();

    // Use drug report headers from config
    const drugReportHeaders = ARABIC_HEADERS.DRUG_REPORTS;

    // Add clinic data sheet if available
    if (clinicData && clinicData.length > 0) {
      const clinicExcelData = [
        drugReportHeaders,
        ...clinicData.map(item => [
          item.ClinicName || item.EntityName,
          item.DrugName,
          item.CategoryName,
          item.DrugUnit || 'غير محدد',
          item.UnitPrice || 0,
          item.TotalQuantity,
          item.TotalCost,
          item.NumberOfCases
        ])
      ];

      const clinicWs = XLSX.utils.aoa_to_sheet(clinicExcelData);

      // Set column widths
      clinicWs['!cols'] = [
        { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.entityName },
        { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.drugName },
        { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.category },
        { width: 12 }, // Unit
        { width: 15 }, // Unit Price
        { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.quantity },
        { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.cost },
        { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.cases }
      ];

      // Apply formatting
      const clinicRange = XLSX.utils.decode_range(clinicWs['!ref'] || 'A1');
      applyCellFormatting(clinicWs, clinicRange);

      XLSX.utils.book_append_sheet(wb, clinicWs, EXCEL_CONFIG.SHEET_NAMES.clinics);
    }

    // Add region data sheet if available
    if (regionData && regionData.length > 0) {
      const regionExcelData = [
        drugReportHeaders,
        ...regionData.map(item => [
          item.RegionName || item.EntityName,
          item.DrugName,
          item.CategoryName,
          item.DrugUnit || 'غير محدد',
          item.UnitPrice || 0,
          item.TotalQuantity,
          item.TotalCost,
          item.NumberOfCases
        ])
      ];

      const regionWs = XLSX.utils.aoa_to_sheet(regionExcelData);

      // Set column widths
      regionWs['!cols'] = [
        { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.entityName },
        { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.drugName },
        { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.category },
        { width: 12 }, // Unit
        { width: 15 }, // Unit Price
        { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.quantity },
        { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.cost },
        { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.cases }
      ];

      // Apply formatting
      const regionRange = XLSX.utils.decode_range(regionWs['!ref'] || 'A1');
      applyCellFormatting(regionWs, regionRange);

      XLSX.utils.book_append_sheet(wb, regionWs, EXCEL_CONFIG.SHEET_NAMES.regions);
    }

    // Add branch data sheet if available
    if (branchData && branchData.length > 0) {
      const branchExcelData = [
        drugReportHeaders,
        ...branchData.map(item => [
          item.BranchName || item.EntityName,
          item.DrugName,
          item.CategoryName,
          item.DrugUnit || 'غير محدد',
          item.UnitPrice || 0,
          item.TotalQuantity,
          item.TotalCost,
          item.NumberOfCases
        ])
      ];

      const branchWs = XLSX.utils.aoa_to_sheet(branchExcelData);

      // Set column widths
      branchWs['!cols'] = [
        { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.entityName },
        { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.drugName },
        { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.category },
        { width: 12 }, // Unit
        { width: 15 }, // Unit Price
        { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.quantity },
        { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.cost },
        { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.cases }
      ];

      // Apply formatting
      const branchRange = XLSX.utils.decode_range(branchWs['!ref'] || 'A1');
      applyCellFormatting(branchWs, branchRange);

      XLSX.utils.book_append_sheet(wb, branchWs, EXCEL_CONFIG.SHEET_NAMES.branches);
    }

    // Create summary sheet if multiple data types exist
    if ((clinicData?.length || 0) + (regionData?.length || 0) + (branchData?.length || 0) > 0) {
      const allData = [
        ...(clinicData || []).map(item => ({ ...item, Type: 'عيادة' })),
        ...(regionData || []).map(item => ({ ...item, Type: 'منطقة' })),
        ...(branchData || []).map(item => ({ ...item, Type: 'فرع' }))
      ];

      // Calculate totals
      const totalQuantity = allData.reduce((sum, item) => sum + (item.TotalQuantity || 0), 0);
      const totalCost = allData.reduce((sum, item) => sum + (item.TotalCost || 0), 0);
      const totalCases = allData.reduce((sum, item) => sum + (item.NumberOfCases || 0), 0);

      const summaryData = [
        ARABIC_HEADERS.SUMMARY,
        ['إجمالي الكمية', totalQuantity.toString()],
        ['إجمالي التكلفة', formatCurrency(totalCost)],
        ['إجمالي عدد الحالات', totalCases.toString()],
        ['عدد العيادات', (clinicData?.length || 0).toString()],
        ['عدد المناطق', (regionData?.length || 0).toString()],
        ['عدد الفروع', (branchData?.length || 0).toString()]
      ];

      const summaryWs = XLSX.utils.aoa_to_sheet(summaryData);

      summaryWs['!cols'] = [
        { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.indicator },
        { width: EXCEL_CONFIG.DEFAULT_COLUMN_WIDTHS.value }
      ];

      // Apply formatting
      const summaryRange = XLSX.utils.decode_range(summaryWs['!ref'] || 'A1');
      applyCellFormatting(summaryWs, summaryRange);

      XLSX.utils.book_append_sheet(wb, summaryWs, EXCEL_CONFIG.SHEET_NAMES.summary);
    }

    // Write the file
    XLSX.writeFile(wb, filename);
    return true;
  } catch (error) {
    console.error('Error exporting drug report to Excel:', error);
    throw error;
  }
};

// Enhanced print function with Arabic support
export const printReport = (elementId: string, title: string) => {
  const element = document.getElementById(elementId);
  if (!element) {
    console.error(ERROR_MESSAGES.ELEMENT_NOT_FOUND);
    throw new Error(ERROR_MESSAGES.ELEMENT_NOT_FOUND);
  }

  try {
    // Create a new window for printing
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    if (!printWindow) {
      throw new Error('فشل في فتح نافذة الطباعة');
    }

    // Get the element's HTML content
    const elementHTML = element.innerHTML;

    // Create the print document
    const printDocument = `
      <!DOCTYPE html>
      <html lang="ar" dir="rtl">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${title}</title>
        <style>
          @media print {
            @page {
              size: A4;
              margin: 20mm;
              direction: rtl;
            }
          }

          body {
            font-family: 'Arial', 'Traditional Arabic', 'Amiri', sans-serif;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 20px;
            background: white;
            color: black;
            font-size: 12pt;
            line-height: 1.4;
          }

          h1, h2, h3, h4, h5, h6 {
            color: black;
            margin-bottom: 10px;
            page-break-after: avoid;
          }

          h1 {
            text-align: center;
            font-size: 18pt;
            margin-bottom: 20px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
          }

          table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            direction: rtl;
            page-break-inside: avoid;
          }

          th, td {
            border: 1px solid #333;
            padding: 8px;
            text-align: right;
            direction: rtl;
            font-size: 11pt;
          }

          th {
            background-color: #f0f0f0;
            font-weight: bold;
            page-break-after: avoid;
          }

          tr {
            page-break-inside: avoid;
          }

          .MuiPaper-root {
            box-shadow: none !important;
            border: 1px solid #ddd;
            margin-bottom: 20px;
          }

          .MuiCard-root {
            box-shadow: none !important;
            border: 1px solid #ddd;
            margin-bottom: 10px;
            padding: 10px;
          }

          .MuiTypography-h4 {
            font-size: 16pt;
            margin-bottom: 15px;
          }

          .MuiTypography-h5 {
            font-size: 14pt;
            margin-bottom: 10px;
          }

          .MuiTypography-h6 {
            font-size: 12pt;
            margin-bottom: 8px;
          }

          /* Hide buttons and interactive elements */
          button, .MuiButton-root, .MuiIconButton-root {
            display: none !important;
          }

          /* Hide DataGrid toolbar */
          .MuiDataGrid-toolbarContainer {
            display: none !important;
          }

          /* Style DataGrid for print */
          .MuiDataGrid-root {
            border: 1px solid #333;
          }

          .MuiDataGrid-columnHeaders {
            background-color: #f0f0f0;
            border-bottom: 2px solid #333;
          }

          .MuiDataGrid-cell {
            border-right: 1px solid #ddd;
            padding: 8px;
            font-size: 11pt;
          }

          /* Page breaks */
          .page-break {
            page-break-before: always;
          }

          .no-break {
            page-break-inside: avoid;
          }

          /* Print-specific styles */
          @media print {
            body {
              -webkit-print-color-adjust: exact;
              color-adjust: exact;
            }

            .MuiPaper-root {
              break-inside: avoid;
            }

            table {
              break-inside: avoid;
            }
          }
        </style>
      </head>
      <body>
        <h1>${title}</h1>
        ${elementHTML}
        <div style="margin-top: 30px; text-align: center; font-size: 10pt; color: #666;">
          تم طباعة هذا التقرير في: ${new Date().toLocaleDateString('ar-SA')} - ${new Date().toLocaleTimeString('ar-SA')}
        </div>
      </body>
      </html>
    `;

    // Write the document to the print window
    printWindow.document.write(printDocument);
    printWindow.document.close();

    // Wait for content to load then print
    printWindow.onload = () => {
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 500);
    };

    return true;
  } catch (error) {
    console.error('Error printing report:', error);
    throw error;
  }
};
