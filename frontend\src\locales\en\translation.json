{"appTitle": "Drug Dispensing Management System", "sections": "Sections", "dispenseDrugs": "Dispense Drugs", "insulin": "<PERSON><PERSON><PERSON>", "drugGroups": "Drug Groups", "monthlyDispense": "Monthly Dispense", "medicalTickets": "Medical Tickets", "courtRulings": "Court Rulings Drugs", "welcomeMessage": "Welcome to the Drug Dispensing App", "management": "Management", "branches": "Branches", "regions": "Regions", "clinics": "Clinics", "drugCategories": "Drug Categories", "drugs": "Drugs", "home": "Home", "branchSelection": "Branch/Region/Clinic Selection", "branchSelectionPage": "Branch, Region, and Clinic Selection Page", "branch": "Branch", "region": "Region", "clinic": "Clinic", "confirmSelection": "Confirm Selection", "loadingData": "Loading data...", "insulinPageContent": "Content for the Insulin Management page will go here.", "drugGroupsPageContent": "Content for the Drug Groups Management page will go here.", "insulinDispense": "Insulin Dispense", "currentBalance": "Current Balance", "quantity": "Quantity", "unitPrice": "Unit Price", "cases": "Cases", "totalCost": "Total Cost", "save": "Save", "drugGroup": "Drug Group", "monthlyCost": "Monthly Cost", "drugGroupsManagement": "Drug Groups Management", "addEditDrugGroup": "Add / Edit Drug Group", "drugGroupName": "Drug Group Name", "update": "Update", "add": "Add", "existingDrugGroups": "Existing Drug Groups", "id": "ID", "actions": "Actions", "insulinManagement": "Insulin Management", "addEditInsulinType": "Add / Edit Insulin Type", "insulinName": "Insulin Name", "supportType": "Support Type", "supported": "Supported", "authority": "Authority", "unit": "Unit", "balance": "Balance", "existingInsulinTypes": "Existing Insulin Types", "branchUpdatedSuccess": "Branch updated successfully!", "branchAddedSuccess": "Branch added successfully!", "branchDeletedSuccess": "Branch deleted successfully!", "branchOperationError": "An error occurred during the branch operation.", "confirmDeleteTitle": "Confirm Deletion", "confirmDeleteBranchMessage": "Are you sure you want to delete this branch? This action cannot be undone.", "cancel": "Cancel", "delete": "Delete", "regionsManagement": "Regions Management", "addEditRegion": "Add / Edit Region", "regionName": "Region Name", "existingRegions": "Existing Regions", "regionUpdatedSuccess": "Region updated successfully!", "regionAddedSuccess": "Region added successfully!", "regionDeletedSuccess": "Region deleted successfully!", "regionOperationError": "An error occurred during the region operation.", "confirmDeleteRegionMessage": "Are you sure you want to delete this region? This action cannot be undone.", "unknown": "Unknown", "branchNameRequired": "Branch name is required", "regionNameRequired": "Region name is required", "branchSelectionRequired": "Branch selection is required", "clinicNameRequired": "Clinic name is required", "regionRequired": "Region is required", "fetchDataError": "Failed to fetch data", "clinicUpdatedSuccess": "Clinic updated successfully!", "clinicAddedSuccess": "Clinic added successfully!", "clinicOperationError": "An error occurred during the clinic operation.", "confirmDeleteClinic": "Are you sure you want to delete this clinic?", "clinicDeletedSuccess": "Clinic deleted successfully!", "clinicDeleteError": "Failed to delete clinic", "clinicsManagement": "Clinics Management", "editClinic": "Edit Clinic", "addNewClinic": "Add New Clinic", "saveChanges": "Save Changes", "affiliatedRegion": "Affiliated Region", "addCategory": "Add Category", "categoryName": "Category Name", "editCategory": "Edit Category", "deleteCategory": "Delete Category", "deleteCategoryConfirmation": "Are you sure you want to delete this category?", "addDrug": "Add Drug", "drugName": "Drug Name", "category": "Category", "editDrug": "Edit Drug", "deleteDrug": "Delete Drug", "deleteDrugConfirmation": "Are you sure you want to delete this drug?", "categoryAddedSuccess": "Category added successfully!", "categoryUpdatedSuccess": "Category updated successfully!", "categoryDeletedSuccess": "Category deleted successfully!", "categoryOperationError": "An error occurred during the category operation.", "categoryNameRequired": "Category name is required", "drugAddedSuccess": "Drug added successfully!", "drugUpdatedSuccess": "Drug updated successfully!", "drugDeletedSuccess": "Drug deleted successfully!", "drugOperationError": "An error occurred during the drug operation.", "drugNameRequired": "Drug name is required", "categoryRequired": "Category is required", "unitRequired": "Unit is required", "fillAllRequiredFields": "Please fill all required fields", "singleAdd": "Single Add", "bulkAdd": "Bulk Add", "bulkMismatchError": "Number of drug names and units do not match.", "drugNamesRequired": "Drug names are required", "drugNamesLabel": "Drug Names", "enterOneDrugNamePerLine": "Enter one drug name per line", "unitsRequired": "Units are required", "unitsLabel": "Units", "enterOneUnitPerLine": "Enter one unit per line", "bulkAddDrugs": "Bulk Add Drugs", "bulkDrugsAddedSuccess": "Bulk drugs added successfully!", "filterByCategory": "Filter by Category", "allCategories": "All Categories", "searchByDrugName": "Search by Drug Name", "drug": "Drug", "dispenseDrug": "Dispense Drug", "dispenseSuccess": "Dispense successful!", "dispenseError": "Error during dispense.", "drugRequired": "Drug is required", "quantityRequired": "Quantity is required", "unitPriceRequired": "Unit price is required", "casesRequired": "Cases are required", "dateRequired": "Date is required", "dispenseDate": "Dispense Date", "dispenseMonth": "Dispense Month", "monthRequired": "Month is required", "addItemToList": "Add to List", "dispenseList": "Dispense List", "emptyListMessage": "List is empty. Please add items.", "saveAll": "Save All", "selectionError": "Selection error. Please ensure correct clinic and drug are selected.", "itemAddedToList": "Item added to list successfully!", "allDispensedSuccess": "All items dispensed successfully!", "emptyDispenseList": "Dispense list is empty. Nothing to save.", "editDispenseItem": "Edit Dispense Item", "itemUpdatedInList": "Item updated in list successfully!", "confirmDeleteItemMessage": "Are you sure you want to delete this item from the list?", "itemDeletedFromList": "Item deleted from list successfully!", "duplicateEntryError": "This dispense entry already exists in the list.", "duplicateEntryErrorBackend": "This dispense entry already exists for the selected clinic, drug, quantity, unit price, and month. Please check history.", "unknownDrug": "Unknown Drug", "unknownClinic": "Unknown Clinic", "dispensedDrugsHistory": "Dispensed Drugs History", "filterByClinic": "Filter by Clinic", "allClinics": "All Clinics", "filterByDrug": "Filter by Drug", "allDrugs": "All Drugs", "filterByMonth": "Filter by Month", "search": "Search", "editDispenseRecord": "Edit Dispense Record", "confirmDeleteRecordMessage": "Are you sure you want to delete this record? This action cannot be undone.", "updateSuccess": "Update successful!", "updateError": "Error during update.", "deleteSuccess": "Delete successful!", "deleteError": "Error during delete.", "filterByRegion": "Filter by Region", "allRegions": "All Regions", "noDataToExport": "No data to export.", "exportToExcel": "Export to Excel", "exportToPDF": "Export to PDF"}