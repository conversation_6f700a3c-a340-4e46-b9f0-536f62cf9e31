import React from 'react';
import { But<PERSON>, <PERSON>, Typography, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material';
import { exportDrugReportToPdf, exportDrugReportToExcel, printReport } from './utils/pdfExport';

// Test data for drug reports
const testClinicData = [
  {
    ClinicName: 'عيادة الرياض الرئيسية',
    DrugName: 'أموكسيسيلين 500 مجم',
    CategoryName: 'مضادات حيوية',
    DrugUnit: 'كبسولة',
    UnitPrice: 2.50,
    TotalQuantity: 1000,
    TotalCost: 2500.00,
    NumberOfCases: 50
  },
  {
    ClinicName: 'عيادة الرياض الرئيسية',
    DrugName: 'باراسيتامول 500 مجم',
    CategoryName: 'مسكنات',
    DrugUnit: 'قرص',
    UnitPrice: 0.75,
    TotalQuantity: 2000,
    TotalCost: 1500.00,
    NumberOfCases: 100
  }
];

const testRegionData = [
  {
    RegionName: 'منطقة الرياض',
    DrugName: 'فيتامين د 1000 وحدة',
    CategoryName: 'فيتامينات',
    DrugUnit: 'كبسولة',
    UnitPrice: 1.25,
    TotalQuantity: 1500,
    TotalCost: 1875.00,
    NumberOfCases: 75
  },
  {
    RegionName: 'منطقة الرياض',
    DrugName: 'أسبرين 100 مجم',
    CategoryName: 'مضادات التجلط',
    DrugUnit: 'قرص',
    UnitPrice: 0.50,
    TotalQuantity: 3000,
    TotalCost: 1500.00,
    NumberOfCases: 120
  }
];

const testBranchData = [
  {
    BranchName: 'فرع الشمال',
    DrugName: 'إنسولين طويل المفعول',
    CategoryName: 'أدوية السكري',
    DrugUnit: 'قلم',
    UnitPrice: 45.00,
    TotalQuantity: 200,
    TotalCost: 9000.00,
    NumberOfCases: 40
  },
  {
    BranchName: 'فرع الشمال',
    DrugName: 'ميتفورمين 500 مجم',
    CategoryName: 'أدوية السكري',
    DrugUnit: 'قرص',
    UnitPrice: 0.25,
    TotalQuantity: 5000,
    TotalCost: 1250.00,
    NumberOfCases: 80
  }
];

const TestDrugExportComponent: React.FC = () => {
  const handleTestPdfExport = async () => {
    try {
      await exportDrugReportToPdf('test-drug-content', 'تقرير-الأدوية-اختبار.pdf', 'تقرير صرف الأدوية - اختبار');
      alert('تم تصدير PDF بنجاح!');
    } catch (error) {
      console.error('خطأ في تصدير PDF:', error);
      alert('خطأ في تصدير PDF');
    }
  };

  const handleTestExcelExport = async () => {
    try {
      await exportDrugReportToExcel(
        testClinicData,
        testRegionData,
        testBranchData,
        'تقرير-الأدوية-اختبار.xlsx',
        'تقرير صرف الأدوية - اختبار',
        'all'
      );
      alert('تم تصدير Excel بنجاح!');
    } catch (error) {
      console.error('خطأ في تصدير Excel:', error);
      alert('خطأ في تصدير Excel');
    }
  };

  const handleTestPrint = () => {
    try {
      printReport('test-drug-content', 'تقرير صرف الأدوية - اختبار');
    } catch (error) {
      console.error('خطأ في الطباعة:', error);
      alert('خطأ في الطباعة');
    }
  };

  return (
    <Box sx={{ p: 3, direction: 'rtl' }} id="test-drug-content">
      <Typography variant="h4" gutterBottom align="center">
        تقرير صرف الأدوية - اختبار
      </Typography>

      {/* Clinic Report */}
      <Paper sx={{ mb: 3 }}>
        <Typography variant="h6" sx={{ p: 2 }}>تقرير العيادات</Typography>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell align="right">اسم العيادة</TableCell>
                <TableCell align="right">اسم الدواء</TableCell>
                <TableCell align="right">الفئة</TableCell>
                <TableCell align="right">الوحدة</TableCell>
                <TableCell align="right">سعر الوحدة</TableCell>
                <TableCell align="right">الكمية الإجمالية</TableCell>
                <TableCell align="right">التكلفة الإجمالية</TableCell>
                <TableCell align="right">عدد الحالات</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {testClinicData.map((row, index) => (
                <TableRow key={index}>
                  <TableCell align="right">{row.ClinicName}</TableCell>
                  <TableCell align="right">{row.DrugName}</TableCell>
                  <TableCell align="right">{row.CategoryName}</TableCell>
                  <TableCell align="right">{row.DrugUnit}</TableCell>
                  <TableCell align="right">{row.UnitPrice.toFixed(2)}</TableCell>
                  <TableCell align="right">{row.TotalQuantity.toLocaleString()}</TableCell>
                  <TableCell align="right">{row.TotalCost.toFixed(2)}</TableCell>
                  <TableCell align="right">{row.NumberOfCases}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Region Report */}
      <Paper sx={{ mb: 3 }}>
        <Typography variant="h6" sx={{ p: 2 }}>تقرير المناطق</Typography>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell align="right">اسم المنطقة</TableCell>
                <TableCell align="right">اسم الدواء</TableCell>
                <TableCell align="right">الفئة</TableCell>
                <TableCell align="right">الوحدة</TableCell>
                <TableCell align="right">سعر الوحدة</TableCell>
                <TableCell align="right">الكمية الإجمالية</TableCell>
                <TableCell align="right">التكلفة الإجمالية</TableCell>
                <TableCell align="right">عدد الحالات</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {testRegionData.map((row, index) => (
                <TableRow key={index}>
                  <TableCell align="right">{row.RegionName}</TableCell>
                  <TableCell align="right">{row.DrugName}</TableCell>
                  <TableCell align="right">{row.CategoryName}</TableCell>
                  <TableCell align="right">{row.DrugUnit}</TableCell>
                  <TableCell align="right">{row.UnitPrice.toFixed(2)}</TableCell>
                  <TableCell align="right">{row.TotalQuantity.toLocaleString()}</TableCell>
                  <TableCell align="right">{row.TotalCost.toFixed(2)}</TableCell>
                  <TableCell align="right">{row.NumberOfCases}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Branch Report */}
      <Paper sx={{ mb: 3 }}>
        <Typography variant="h6" sx={{ p: 2 }}>تقرير الفروع</Typography>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell align="right">اسم الفرع</TableCell>
                <TableCell align="right">اسم الدواء</TableCell>
                <TableCell align="right">الفئة</TableCell>
                <TableCell align="right">الوحدة</TableCell>
                <TableCell align="right">سعر الوحدة</TableCell>
                <TableCell align="right">الكمية الإجمالية</TableCell>
                <TableCell align="right">التكلفة الإجمالية</TableCell>
                <TableCell align="right">عدد الحالات</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {testBranchData.map((row, index) => (
                <TableRow key={index}>
                  <TableCell align="right">{row.BranchName}</TableCell>
                  <TableCell align="right">{row.DrugName}</TableCell>
                  <TableCell align="right">{row.CategoryName}</TableCell>
                  <TableCell align="right">{row.DrugUnit}</TableCell>
                  <TableCell align="right">{row.UnitPrice.toFixed(2)}</TableCell>
                  <TableCell align="right">{row.TotalQuantity.toLocaleString()}</TableCell>
                  <TableCell align="right">{row.TotalCost.toFixed(2)}</TableCell>
                  <TableCell align="right">{row.NumberOfCases}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Export and Print Buttons */}
      <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', mt: 3 }}>
        <Button 
          variant="contained" 
          color="success" 
          onClick={handleTestPdfExport}
          size="large"
        >
          اختبار تصدير PDF
        </Button>
        <Button 
          variant="contained" 
          color="info" 
          onClick={handleTestExcelExport}
          size="large"
        >
          اختبار تصدير Excel
        </Button>
        <Button 
          variant="contained" 
          color="primary" 
          onClick={handleTestPrint}
          size="large"
        >
          اختبار الطباعة
        </Button>
      </Box>
    </Box>
  );
};

export default TestDrugExportComponent;
