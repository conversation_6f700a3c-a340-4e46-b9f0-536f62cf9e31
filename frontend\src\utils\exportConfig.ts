// Export configuration and constants

export const PDF_CONFIG = {
  // Page settings
  PAGE_WIDTH: 210, // A4 width in mm
  PAGE_HEIGHT: 297, // A4 height in mm
  MARGIN_TOP: 25, // Top margin after title
  
  // Quality settings
  SCALE: 2, // High quality scaling
  IMAGE_QUALITY: 1.0, // PNG quality
  
  // Timeout settings
  RENDER_DELAY: 2000, // Wait time for content to render
  IMAGE_TIMEOUT: 20000, // html2canvas timeout
  
  // Font settings
  TITLE_FONT_SIZE: 16,
  DEFAULT_FONT: 'Arial',
  FALLBACK_FONT: 'helvetica'
};

export const EXCEL_CONFIG = {
  // Column widths
  DEFAULT_COLUMN_WIDTHS: {
    entityName: 20,
    category: 15,
    drugName: 25,
    quantity: 15,
    cost: 15,
    cases: 15,
    month: 15,
    indicator: 25,
    value: 20
  },
  
  // Colors
  HEADER_BACKGROUND: 'E6E6FA', // Light purple
  BORDER_COLOR: '000000', // Black
  
  // Font settings
  DEFAULT_FONT: 'Arial',
  HEADER_FONT_SIZE: 12,
  CELL_FONT_SIZE: 11,
  
  // Sheet names in Arabic
  SHEET_NAMES: {
    comparison: 'بيانات المقارنة',
    trends: 'الاتجاهات الشهرية',
    kpi: 'المؤشرات الرئيسية',
    clinics: 'تقرير العيادات',
    regions: 'تقرير المناطق',
    branches: 'تقرير الفروع',
    summary: 'ملخص التقرير'
  }
};

export const ARABIC_HEADERS = {
  // Comparison data headers
  COMPARISON: [
    'اسم الكيان',
    'الفئة', 
    'اسم الدواء',
    'الكمية الإجمالية',
    'التكلفة الإجمالية',
    'عدد الحالات'
  ],
  
  // Trend data headers
  TRENDS: [
    'الشهر',
    'الكمية الإجمالية',
    'التكلفة الإجمالية',
    'عدد الحالات'
  ],
  
  // KPI headers
  KPI: ['المؤشر', 'القيمة'],
  
  // KPI labels
  KPI_LABELS: {
    totalCost: 'التكلفة الإجمالية',
    totalQuantity: 'الكمية الإجمالية',
    totalCases: 'عدد الحالات',
    avgCostPerCase: 'متوسط التكلفة لكل حالة'
  },

  // Drug report headers
  DRUG_REPORTS: [
    'اسم الكيان',
    'اسم الدواء',
    'الفئة',
    'الوحدة',
    'سعر الوحدة',
    'الكمية الإجمالية',
    'التكلفة الإجمالية',
    'عدد الحالات'
  ],

  // Summary report headers
  SUMMARY: ['نوع التقرير', 'القيمة']
};

export const CSS_STYLES = {
  PDF_EXPORT: `
    .pdf-export-rtl {
      direction: rtl !important;
      text-align: right !important;
      font-family: 'Arial', 'Traditional Arabic', 'Amiri', sans-serif !important;
      background: white !important;
    }
    .pdf-export-rtl * {
      font-family: 'Arial', 'Traditional Arabic', 'Amiri', sans-serif !important;
    }
    .pdf-export-rtl .MuiDataGrid-root {
      direction: rtl !important;
    }
    .pdf-export-rtl .MuiDataGrid-columnHeaders {
      direction: rtl !important;
    }
    .pdf-export-rtl .MuiDataGrid-cell {
      text-align: right !important;
      direction: rtl !important;
    }
    .pdf-export-rtl table {
      direction: rtl !important;
      border-collapse: collapse !important;
      width: 100% !important;
    }
    .pdf-export-rtl th, .pdf-export-rtl td {
      text-align: right !important;
      border: 1px solid #ddd !important;
      padding: 8px !important;
      direction: rtl !important;
    }
    .pdf-export-rtl svg {
      background: white !important;
    }
    .pdf-export-rtl .recharts-wrapper {
      background: white !important;
    }
    .pdf-export-rtl .MuiPaper-root {
      box-shadow: none !important;
      border: 1px solid #ddd !important;
    }
    .pdf-export-rtl .MuiCard-root {
      box-shadow: none !important;
      border: 1px solid #ddd !important;
    }
  `
};

// Utility functions
export const generateFilename = (prefix: string, extension: string): string => {
  const date = new Date().toISOString().split('T')[0];
  return `${prefix}-${date}.${extension}`;
};

export const formatCurrency = (value: number): string => {
  return value.toFixed(2);
};

export const formatNumber = (value: number): string => {
  return value.toLocaleString();
};

// Error messages in Arabic
export const ERROR_MESSAGES = {
  ELEMENT_NOT_FOUND: 'العنصر المحدد غير موجود',
  NO_DATA_TO_EXPORT: 'لا توجد بيانات للتصدير',
  PDF_EXPORT_ERROR: 'خطأ في تصدير ملف PDF',
  EXCEL_EXPORT_ERROR: 'خطأ في تصدير ملف Excel',
  PDF_EXPORT_SUCCESS: 'تم تصدير ملف PDF بنجاح',
  EXCEL_EXPORT_SUCCESS: 'تم تصدير ملف Excel بنجاح',
  PRINT_ERROR: 'خطأ في طباعة التقرير',
  PRINT_SUCCESS: 'تم إرسال التقرير للطباعة',
  NO_DATA_TO_PRINT: 'لا توجد بيانات للطباعة'
};

// Export types
export interface ExportData {
  EntityName: string;
  CategoryName: string;
  DrugName: string;
  TotalQuantity: number;
  TotalCost: number;
  NumberOfCases: number;
  Month?: string;
}

export interface TrendData {
  Month: string;
  TotalQuantity: number;
  TotalCost: number;
  NumberOfCases: number;
}

export interface KPIData {
  totalCost: number;
  totalQuantity: number;
  totalCases: number;
}

export interface ExcelCellStyle {
  border: {
    top: { style: string; color: { rgb: string } };
    bottom: { style: string; color: { rgb: string } };
    left: { style: string; color: { rgb: string } };
    right: { style: string; color: { rgb: string } };
  };
  alignment: { horizontal: string; vertical: string };
  font: { name: string; sz: number; bold?: boolean };
  fill?: { fgColor: { rgb: string } };
}
