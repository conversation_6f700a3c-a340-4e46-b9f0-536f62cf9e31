import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Snackbar,
  Alert,
  Button,
  TextField, // Added TextField
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import dayjs, { Dayjs } from 'dayjs';

// Interfaces for data from backend
interface Branch {
  id: number;
  name: string;
}

interface Region {
  id: number;
  name: string;
  branch_id: number;
}

interface Clinic {
  id: number;
  name: string;
  region_id: number;
}

interface Drug {
  DrugID: number;
  DrugName: string;
  Unit: string | null;
  CategoryID: number;
}

interface DrugCategory {
  CategoryID: number;
  CategoryName: string;
}

interface AggregatedDispensedDrugClinic {
  ClinicID: number;
  ClinicName: string;
  DrugID: number;
  DrugName: string;
  DrugUnit: string | null;
  CategoryID: number;
  CategoryName: string;
  UnitPrice: number;
  TotalQuantity: number;
  TotalCost: number;
  NumberOfCases: number;
}

interface AggregatedDispensedDrugRegion {
  RegionID: number;
  RegionName: string;
  DrugID: number;
  DrugName: string;
  DrugUnit: string | null;
  CategoryID: number;
  CategoryName: string;
  UnitPrice: number;
  TotalQuantity: number;
  TotalCost: number;
  NumberOfCases: number;
}

interface AggregatedDispensedDrugBranch {
  BranchID: number;
  BranchName: string;
  DrugID: number;
  DrugName: string;
  DrugUnit: string | null;
  CategoryID: number;
  CategoryName: string;
  UnitPrice: number;
  TotalQuantity: number;
  TotalCost: number;
  NumberOfCases: number;
}

const DispensedDrugsReportsPage = () => {
  const { t } = useTranslation();
  const [branches, setBranches] = useState<Branch[]>([]);
  const [regions, setRegions] = useState<Region[]>([]);
  const [clinics, setClinics] = useState<Clinic[]>([]);
  const [drugs, setDrugs] = useState<Drug[]>([]);
  const [categories, setCategories] = useState<DrugCategory[]>([]);

  const [filterBranchId, setFilterBranchId] = useState<number | ''>('');
  const [filterRegionId, setFilterRegionId] = useState<number | ''>('');
  const [filterClinicId, setFilterClinicId] = useState<number | ''>('');
  const [filterStartDate, setFilterStartDate] = useState<Dayjs | null>(null);
  const [filterMonth, setFilterMonth] = useState<Dayjs | null>(null);
  const [filterEndDate, setFilterEndDate] = useState<Dayjs | null>(null);
  const [filterQuarter, setFilterQuarter] = useState<number | ''>('');
  const [filterYearForQuarter, setFilterYearForQuarter] = useState<number | ''>(new Date().getFullYear());

  const [clinicAggregatedData, setClinicAggregatedData] = useState<AggregatedDispensedDrugClinic[]>([]);
  const [regionAggregatedData, setRegionAggregatedData] = useState<AggregatedDispensedDrugRegion[]>([]);
  const [branchAggregatedData, setBranchAggregatedData] = useState<AggregatedDispensedDrugBranch[]>([]);

  const [snackbar, setSnackbar] = useState<{ open: boolean, message: string, severity: 'success' | 'error' } | null>(null);

  useEffect(() => {
    fetchBranches();
    fetchRegions();
    fetchClinics();
    fetchDrugs();
    fetchCategories();
  }, []);

  useEffect(() => {
    fetchAggregatedData();
  }, [filterBranchId, filterRegionId, filterClinicId, filterMonth, filterStartDate, filterEndDate, filterQuarter, filterYearForQuarter]);

  const fetchBranches = async () => {
    try {
      const response = await axios.get<Branch[]>('http://localhost:8000/branches/');
      setBranches(response.data);
    } catch (error) {
      console.error('Error fetching branches:', error);
      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });
    }
  };

  const fetchRegions = async () => {
    try {
      const response = await axios.get<Region[]>('http://localhost:8000/regions/');
      setRegions(response.data);
    } catch (error) {
      console.error('Error fetching regions:', error);
      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });
    }
  };

  const fetchClinics = async () => {
    try {
      const response = await axios.get<Clinic[]>('http://localhost:8000/clinics/');
      setClinics(response.data);
    } catch (error) {
      console.error('Error fetching clinics:', error);
      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });
    }
  };

  const fetchDrugs = async () => {
    try {
      const response = await axios.get<Drug[]>('http://localhost:8000/drugs/');
      setDrugs(response.data);
    } catch (error) {
      console.error('Error fetching drugs:', error);
      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await axios.get<DrugCategory[]>('http://localhost:8000/drug-categories/');
      setCategories(response.data);
    } catch (error) {
      console.error('Error fetching drug categories:', error);
      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });
    }
  };

  const fetchAggregatedData = async () => {
    const monthParam = filterMonth ? filterMonth.format('YYYY-MM') : '';
    const startDateParam = filterStartDate ? filterStartDate.format('YYYY-MM') : '';
    const endDateParam = filterEndDate ? filterEndDate.format('YYYY-MM') : '';
    const quarterParam = filterQuarter ? `${filterYearForQuarter}-Q${filterQuarter}` : '';

    const params = {
      ...(filterBranchId && { branch_id: filterBranchId }),
      ...(filterRegionId && { region_id: filterRegionId }),
      ...(filterClinicId && { clinic_id: filterClinicId }),
      ...(monthParam && { month: monthParam }),
      ...(startDateParam && { start_month: startDateParam }),
      ...(endDateParam && { end_month: endDateParam }),
      ...(quarterParam && { quarter: quarterParam }),
    };

    try {
      // Fetch by Clinic
      const clinicResponse = await axios.get<AggregatedDispensedDrugClinic[]>(
        'http://localhost:8000/reports/dispensed-drugs/by-clinic',
        { params }
      );
      setClinicAggregatedData(clinicResponse.data);

      // Fetch by Region (if not filtering by specific clinic)
      if (!filterClinicId) {
        const regionResponse = await axios.get<AggregatedDispensedDrugRegion[]>(
          'http://localhost:8000/reports/dispensed-drugs/by-region',
          { params }
        );
        setRegionAggregatedData(regionResponse.data);
      } else {
        setRegionAggregatedData([]); // Clear if clinic filter is active
      }

      // Fetch by Branch (if not filtering by specific clinic or region)
      if (!filterClinicId && !filterRegionId) {
        const branchResponse = await axios.get<AggregatedDispensedDrugBranch[]>(
          'http://localhost:8000/reports/dispensed-drugs/by-branch',
          { params }
        );
        setBranchAggregatedData(branchResponse.data);
      } else {
        setBranchAggregatedData([]); // Clear if clinic or region filter is active
      }

    } catch (error) {
      console.error('Error fetching aggregated data:', error);
      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });
    }
  };

  const handleResetFilters = () => {
    setFilterBranchId('');
    setFilterRegionId('');
    setFilterClinicId('');
    setFilterMonth(null);
    setFilterStartDate(null);
    setFilterEndDate(null);
    setFilterQuarter('');
    setFilterYearForQuarter(new Date().getFullYear());
  };

  const getClinicName = (clinicId: number) => {
    const clinic = clinics.find(c => c.id === clinicId);
    return clinic ? clinic.name : t('unknownClinic');
  };

  const getRegionName = (regionId: number) => {
    const region = regions.find(r => r.id === regionId);
    return region ? region.name : t('unknownRegion');
  };

  const getBranchName = (branchId: number) => {
    const branch = branches.find(b => b.id === branchId);
    return branch ? branch.name : t('unknownBranch');
  };

  const getDrugName = (drugId: number, drugUnit: string | null) => {
    const drug = drugs.find(d => d.DrugID === drugId);
    return drug ? `${drug.DrugName} (${drugUnit || drug.Unit || 'N/A'})` : t('unknownDrug');
  };

  const getCategoryName = (categoryId: number) => {
    const category = categories.find(c => c.CategoryID === categoryId);
    return category ? category.CategoryName : t('unknownCategory');
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          {t('dispensedDrugsReports')}
        </Typography>

        <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
          <FormControl sx={{ minWidth: 180 }}>
            <InputLabel>{t('filterByBranch')}</InputLabel>
            <Select
              value={filterBranchId}
              label={t('filterByBranch')}
              onChange={(e) => {
                setFilterBranchId(e.target.value as number);
                setFilterRegionId(''); // Reset region when branch changes
                setFilterClinicId(''); // Reset clinic when branch changes
              }}
            >
              <MenuItem value="">{t('allBranches')}</MenuItem>
              {branches.map((branch) => (
                <MenuItem key={branch.id} value={branch.id}>
                  {branch.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl sx={{ minWidth: 180 }}>
            <InputLabel>{t('filterByRegion')}</InputLabel>
            <Select
              value={filterRegionId}
              label={t('filterByRegion')}
              onChange={(e) => {
                setFilterRegionId(e.target.value as number);
                setFilterClinicId(''); // Reset clinic when region changes
              }}
            >
              <MenuItem value="">{t('allRegions')}</MenuItem>
              {regions
                .filter(region => filterBranchId === '' || region.branch_id === filterBranchId)
                .map((region) => (
                  <MenuItem key={region.id} value={region.id}>
                    {region.name}
                  </MenuItem>
                ))}
            </Select>
          </FormControl>

          <FormControl sx={{ minWidth: 180 }}>
            <InputLabel>{t('filterByClinic')}</InputLabel>
            <Select
              value={filterClinicId}
              label={t('filterByClinic')}
              onChange={(e) => setFilterClinicId(e.target.value as number)}
            >
              <MenuItem value="">{t('allClinics')}</MenuItem>
              {clinics
                .filter(clinic => filterRegionId === '' || clinic.region_id === filterRegionId)
                .map((clinic) => (
                  <MenuItem key={clinic.id} value={clinic.id}>
                    {clinic.name}
                  </MenuItem>
                ))}
            </Select>
          </FormControl>

          <DatePicker
            label={t('filterByMonth')}
            views={['year', 'month']}
            value={filterMonth}
            onChange={(value: Dayjs | null) => setFilterMonth(value)}
            slots={{ textField: TextField }}
            slotProps={{
              textField: {
                fullWidth: true,
                sx: { minWidth: 180 }
              }
            }}
          />

          <DatePicker
            label={t('filterByStartMonth')}
            views={['year', 'month']}
            value={filterStartDate}
            onChange={(value: Dayjs | null) => setFilterStartDate(value)}
            slots={{ textField: TextField }}
            slotProps={{
              textField: {
                fullWidth: true,
                sx: { minWidth: 180 }
              }
            }}
          />

          <DatePicker
            label={t('filterByEndMonth')}
            views={['year', 'month']}
            value={filterEndDate}
            onChange={(value: Dayjs | null) => setFilterEndDate(value)}
            slots={{ textField: TextField }}
            slotProps={{
              textField: {
                fullWidth: true,
                sx: { minWidth: 180 }
              }
            }}
          />

          <FormControl sx={{ minWidth: 180 }}>
            <InputLabel>{t('filterByQuarter')}</InputLabel>
            <Select
              value={filterQuarter}
              label={t('filterByQuarter')}
              onChange={(e) => setFilterQuarter(e.target.value as number)}
            >
              <MenuItem value="">{t('allQuarters')}</MenuItem>
              <MenuItem value={1}>{t('quarter1')}</MenuItem>
              <MenuItem value={2}>{t('quarter2')}</MenuItem>
              <MenuItem value={3}>{t('quarter3')}</MenuItem>
              <MenuItem value={4}>{t('quarter4')}</MenuItem>
            </Select>
          </FormControl>

          <FormControl sx={{ minWidth: 120 }}>
            <TextField
              label={t('yearForQuarter')}
              type="number"
              value={filterYearForQuarter}
              onChange={(e) => setFilterYearForQuarter(parseInt(e.target.value, 10))}
            />
          </FormControl>

          <Button
            variant="outlined"
            color="secondary"
            onClick={handleResetFilters}
          >
            {t('resetFilters')}
          </Button>
        </Box>

        {/* Display Aggregated Data */}
        {filterClinicId ? (
          // Display Clinic-level aggregation if a specific clinic is selected
          <Box>
            <Typography variant="h5" gutterBottom sx={{ mt: 4 }}>
              {t('clinicReportFor')} {getClinicName(filterClinicId as number)}
            </Typography>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>{t('drug')}</TableCell>
                    <TableCell>{t('category')}</TableCell>
                    <TableCell>{t('unitPrice')}</TableCell>
                    <TableCell>{t('totalQuantity')}</TableCell>
                    <TableCell>{t('numberOfCases')}</TableCell>
                    <TableCell>{t('totalCost')}</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {clinicAggregatedData.map((data, index) => (
                    <TableRow key={index}>
                      <TableCell>{getDrugName(data.DrugID, data.DrugUnit)}</TableCell>
                      <TableCell>{getCategoryName(data.CategoryID)}</TableCell>
                      <TableCell>{data.UnitPrice}</TableCell>
                      <TableCell>{data.TotalQuantity}</TableCell>
                      <TableCell>{data.NumberOfCases}</TableCell>
                      <TableCell>{data.TotalCost}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        ) : filterRegionId ? (
          // Display Region-level aggregation if a specific region is selected (and no clinic)
          <Box>
            <Typography variant="h5" gutterBottom sx={{ mt: 4 }}>
              {t('regionReportFor')} {getRegionName(filterRegionId as number)}
            </Typography>
            {/* Aggregate by Clinic within the selected Region */}
            {regions
              .filter(r => r.id === filterRegionId)
              .map(selectedRegion => (
                <Box key={selectedRegion.id} sx={{ mb: 4 }}>
                  {clinics
                    .filter(clinic => clinic.region_id === selectedRegion.id)
                    .map(clinic => {
                      const clinicData = clinicAggregatedData.filter(d => d.ClinicID === clinic.id);
                      if (clinicData.length === 0) return null;
                      return (
                        <Box key={clinic.id} sx={{ mb: 2 }}>
                          <Typography variant="h6" sx={{ mt: 2 }}>
                            {t('clinic')}: {clinic.name}
                          </Typography>
                          <TableContainer component={Paper}>
                            <Table size="small">
                              <TableHead>
                                <TableRow>
                                  <TableCell>{t('drug')}</TableCell>
                                  <TableCell>{t('category')}</TableCell>
                                  <TableCell>{t('unitPrice')}</TableCell>
                                  <TableCell>{t('totalQuantity')}</TableCell>
                                  <TableCell>{t('totalCost')}</TableCell>
                                </TableRow>
                              </TableHead>
                              <TableBody>
                                {clinicData.map((data, index) => (
                                  <TableRow key={index}>
                                    <TableCell>{getDrugName(data.DrugID, data.DrugUnit)}</TableCell>
                                    <TableCell>{getCategoryName(data.CategoryID)}</TableCell>
                                    <TableCell>{data.UnitPrice}</TableCell>
                                    <TableCell>{data.TotalQuantity}</TableCell>
                                    <TableCell>{data.NumberOfCases}</TableCell>
                                    <TableCell>{data.TotalCost}</TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </TableContainer>
                        </Box>
                      );
                    })}
                  <Typography variant="h6" sx={{ mt: 4 }}>
                    {t('totalForRegion')}: {selectedRegion.name}
                  </Typography>
                  <TableContainer component={Paper}>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>{t('drug')}</TableCell>
                          <TableCell>{t('category')}</TableCell>
                          <TableCell>{t('unitPrice')}</TableCell>
                          <TableCell>{t('totalQuantity')}</TableCell>
                          <TableCell>{t('totalCost')}</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {regionAggregatedData.map((data, index) => (
                          <TableRow key={index}>
                            <TableCell>{getDrugName(data.DrugID, data.DrugUnit)}</TableCell>
                            <TableCell>{getCategoryName(data.CategoryID)}</TableCell>
                            <TableCell>{data.UnitPrice}</TableCell>
                            <TableCell>{data.TotalQuantity}</TableCell>
                            <TableCell>{data.TotalCost}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Box>
              ))}
          </Box>
        ) : filterBranchId ? (
          // Display Branch-level aggregation if a specific branch is selected (and no clinic/region)
          <Box>
            <Typography variant="h5" gutterBottom sx={{ mt: 4 }}>
              {t('branchReportFor')} {getBranchName(filterBranchId as number)}
            </Typography>
            {/* Aggregate by Region within the selected Branch */}
            {branches
              .filter(b => b.id === filterBranchId)
              .map(selectedBranch => (
                <Box key={selectedBranch.id} sx={{ mb: 4 }}>
                  {regions
                    .filter(region => region.branch_id === selectedBranch.id)
                    .map(region => {
                      const regionData = regionAggregatedData.filter(d => d.RegionID === region.id);
                      if (regionData.length === 0) return null;
                      return (
                        <Box key={region.id} sx={{ mb: 2 }}>
                          <Typography variant="h6" sx={{ mt: 2 }}>
                            {t('region')}: {region.name}
                          </Typography>
                          <TableContainer component={Paper}>
                            <Table size="small">
                              <TableHead>
                                <TableRow>
                                  <TableCell>{t('drug')}</TableCell>
                                  <TableCell>{t('category')}</TableCell>
                                  <TableCell>{t('unitPrice')}</TableCell>
                                  <TableCell>{t('totalQuantity')}</TableCell>
                                  <TableCell>{t('totalCost')}</TableCell>
                                </TableRow>
                              </TableHead>
                              <TableBody>
                                {regionData.map((data, index) => (
                                  <TableRow key={index}>
                                    <TableCell>{getDrugName(data.DrugID, data.DrugUnit)}</TableCell>
                                    <TableCell>{getCategoryName(data.CategoryID)}</TableCell>
                                    <TableCell>{data.UnitPrice}</TableCell>
                                    <TableCell>{data.TotalQuantity}</TableCell>
                                    <TableCell>{data.NumberOfCases}</TableCell>
                                    <TableCell>{data.TotalCost}</TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </TableContainer>
                        </Box>
                      );
                    })}
                  <Typography variant="h6" sx={{ mt: 4 }}>
                    {t('totalForBranch')}: {selectedBranch.name}
                  </Typography>
                  <TableContainer component={Paper}>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>{t('drug')}</TableCell>
                          <TableCell>{t('category')}</TableCell>
                          <TableCell>{t('unitPrice')}</TableCell>
                          <TableCell>{t('totalQuantity')}</TableCell>
                          <TableCell>{t('totalCost')}</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {branchAggregatedData.map((data, index) => (
                          <TableRow key={index}>
                            <TableCell>{getDrugName(data.DrugID, data.DrugUnit)}</TableCell>
                            <TableCell>{getCategoryName(data.CategoryID)}</TableCell>
                            <TableCell>{data.UnitPrice}</TableCell>
                            <TableCell>{data.TotalQuantity}</TableCell>
                            <TableCell>{data.NumberOfCases}</TableCell>
                            <TableCell>{data.TotalCost}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Box>
              ))}
          </Box>
        ) : (
          // Default view or message if no filters are applied
          <Typography variant="body1" sx={{ mt: 4 }}>
            {t('selectFiltersForReport')}
          </Typography>
        )}

        {snackbar && (
          <Snackbar
            open={snackbar.open}
            autoHideDuration={6000}
            onClose={() => setSnackbar(null)}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
          >
            <Alert onClose={() => setSnackbar(null)} severity={snackbar.severity} sx={{ width: '100%' }}>
              {snackbar.message}
            </Alert>
          </Snackbar>
        )}
      </Box>
    </LocalizationProvider>
  );
};

export default DispensedDrugsReportsPage;
