<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التصدير</title>
    <style>
        body {
            font-family: 'Arial', 'Traditional Arabic', 'Amiri', sans-serif;
            direction: rtl;
            text-align: right;
            margin: 20px;
            background: white;
        }
        .test-content {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            direction: rtl;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .chart-placeholder {
            width: 100%;
            height: 200px;
            background: #f0f0f0;
            border: 1px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div id="test-export-content">
        <h1>تقرير مقارنة الأدوية</h1>
        
        <div class="test-content">
            <h2>المؤشرات الرئيسية</h2>
            <table>
                <tr>
                    <th>المؤشر</th>
                    <th>القيمة</th>
                </tr>
                <tr>
                    <td>التكلفة الإجمالية</td>
                    <td>150,000.00</td>
                </tr>
                <tr>
                    <td>الكمية الإجمالية</td>
                    <td>5,000</td>
                </tr>
                <tr>
                    <td>عدد الحالات</td>
                    <td>250</td>
                </tr>
            </table>
        </div>

        <div class="test-content">
            <h2>بيانات المقارنة</h2>
            <table>
                <tr>
                    <th>اسم الكيان</th>
                    <th>الفئة</th>
                    <th>اسم الدواء</th>
                    <th>الكمية الإجمالية</th>
                    <th>التكلفة الإجمالية</th>
                    <th>عدد الحالات</th>
                </tr>
                <tr>
                    <td>عيادة الرياض</td>
                    <td>مضادات حيوية</td>
                    <td>أموكسيسيلين</td>
                    <td>1,000</td>
                    <td>25,000.00</td>
                    <td>50</td>
                </tr>
                <tr>
                    <td>عيادة جدة</td>
                    <td>مسكنات</td>
                    <td>باراسيتامول</td>
                    <td>2,000</td>
                    <td>15,000.00</td>
                    <td>100</td>
                </tr>
                <tr>
                    <td>عيادة الدمام</td>
                    <td>فيتامينات</td>
                    <td>فيتامين د</td>
                    <td>1,500</td>
                    <td>30,000.00</td>
                    <td>75</td>
                </tr>
            </table>
        </div>

        <div class="test-content">
            <h2>الرسم البياني</h2>
            <div class="chart-placeholder">
                رسم بياني تجريبي
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <script>
        // Test PDF export function
        async function testPdfExport() {
            const element = document.getElementById('test-export-content');
            
            try {
                const canvas = await html2canvas(element, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    logging: false
                });

                const imgData = canvas.toDataURL('image/png', 1.0);
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF('p', 'mm', 'a4');
                
                const imgWidth = 210;
                const pageHeight = 297;
                const imgHeight = (canvas.height * imgWidth) / canvas.width;
                let heightLeft = imgHeight;
                let position = 0;

                pdf.setFontSize(16);
                pdf.text('تقرير مقارنة الأدوية', imgWidth / 2, 15, { align: 'center' });
                position = 25;

                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                heightLeft -= pageHeight - position;

                while (heightLeft >= 0) {
                    position = heightLeft - imgHeight;
                    pdf.addPage();
                    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                    heightLeft -= pageHeight;
                }

                pdf.save('test-report.pdf');
                alert('تم تصدير PDF بنجاح!');
            } catch (error) {
                console.error('خطأ في تصدير PDF:', error);
                alert('خطأ في تصدير PDF');
            }
        }

        // Test Excel export function
        function testExcelExport() {
            try {
                const wb = XLSX.utils.book_new();
                
                const data = [
                    ['اسم الكيان', 'الفئة', 'اسم الدواء', 'الكمية الإجمالية', 'التكلفة الإجمالية', 'عدد الحالات'],
                    ['عيادة الرياض', 'مضادات حيوية', 'أموكسيسيلين', 1000, 25000.00, 50],
                    ['عيادة جدة', 'مسكنات', 'باراسيتامول', 2000, 15000.00, 100],
                    ['عيادة الدمام', 'فيتامينات', 'فيتامين د', 1500, 30000.00, 75]
                ];

                const ws = XLSX.utils.aoa_to_sheet(data);
                
                // Apply formatting
                const range = XLSX.utils.decode_range(ws['!ref']);
                ws['!cols'] = [
                    { width: 20 }, { width: 15 }, { width: 25 },
                    { width: 15 }, { width: 15 }, { width: 15 }
                ];

                for (let row = range.s.r; row <= range.e.r; row++) {
                    for (let col = range.s.c; col <= range.e.c; col++) {
                        const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                        if (!ws[cellAddress]) ws[cellAddress] = { t: 's', v: '' };
                        
                        ws[cellAddress].s = {
                            border: {
                                top: { style: 'thin', color: { rgb: '000000' } },
                                bottom: { style: 'thin', color: { rgb: '000000' } },
                                left: { style: 'thin', color: { rgb: '000000' } },
                                right: { style: 'thin', color: { rgb: '000000' } }
                            },
                            alignment: { horizontal: 'right', vertical: 'center' },
                            font: { name: 'Arial', sz: 11 }
                        };

                        if (row === 0) {
                            ws[cellAddress].s.fill = { fgColor: { rgb: 'E6E6FA' } };
                            ws[cellAddress].s.font = { name: 'Arial', sz: 12, bold: true };
                        }
                    }
                }

                XLSX.utils.book_append_sheet(wb, ws, 'بيانات المقارنة');
                XLSX.writeFile(wb, 'test-report.xlsx');
                alert('تم تصدير Excel بنجاح!');
            } catch (error) {
                console.error('خطأ في تصدير Excel:', error);
                alert('خطأ في تصدير Excel');
            }
        }
    </script>

    <div style="margin-top: 30px; text-align: center;">
        <button onclick="testPdfExport()" style="padding: 10px 20px; margin: 10px; font-size: 16px;">
            اختبار تصدير PDF
        </button>
        <button onclick="testExcelExport()" style="padding: 10px 20px; margin: 10px; font-size: 16px;">
            اختبار تصدير Excel
        </button>
    </div>
</body>
</html>
