import React, { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import {
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Snackbar,
  Alert,
  Button,
  TextField,
  OutlinedInput,
  Checkbox,
  ListItemText,
  Grid,
  Paper,
  Card,
  CardContent,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';
import { DataGrid, GridColDef, GridToolbar } from '@mui/x-data-grid';
import dayjs, { Dayjs } from 'dayjs';
import { exportComparisonReportToPdf, exportComparisonReportToExcel } from '../utils/pdfExport';

// Interfaces
interface Branch { id: number; name: string; }
interface Region { id: number; name: string; branch_id: number; }
interface Clinic { id: number; name: string; region_id: number; }
interface DrugCategory { CategoryID: number; CategoryName: string; }
interface Drug { DrugID: number; DrugName: string; CategoryID: number; Unit: string | null; }
interface ComparisonDispensedDrugData {
  id: string; // Required for DataGrid
  EntityID: number;
  EntityName: string;
  DrugID: number;
  DrugName: string;
  DrugUnit: string | null;
  CategoryID: number;
  CategoryName: string;
  TotalQuantity: number;
  TotalCost: number;
  NumberOfCases: number;
}
interface ComparisonTrendData {
  Month: string;
  TotalQuantity: number;
  TotalCost: number;
  NumberOfCases: number;
}

const ComparisonReportsPage = () => {
  const { t } = useTranslation();
  const [branches, setBranches] = useState<Branch[]>([]);
  const [regions, setRegions] = useState<Region[]>([]);
  const [clinics, setClinics] = useState<Clinic[]>([]);
  const [categories, setCategories] = useState<DrugCategory[]>([]);

  // Filters
  const [compareEntityType, setCompareEntityType] = useState<'clinic' | 'region' | 'branch'>('clinic');
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [filterCategoryId, setFilterCategoryId] = useState<number | ''>('');
  const [filterStartDate, setFilterStartDate] = useState<Dayjs | null>(dayjs().subtract(1, 'month'));
  const [filterEndDate, setFilterEndDate] = useState<Dayjs | null>(dayjs());

  // Data
  const [comparisonData, setComparisonData] = useState<ComparisonDispensedDrugData[]>([]);
  const [trendData, setTrendData] = useState<ComparisonTrendData[]>([]);
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState<{ open: boolean, message: string, severity: 'success' | 'error' } | null>(null);

  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [branchesRes, regionsRes, clinicsRes, categoriesRes] = await Promise.all([
          axios.get<Branch[]>('http://localhost:8000/branches/'),
          axios.get<Region[]>('http://localhost:8000/regions/'),
          axios.get<Clinic[]>('http://localhost:8000/clinics/'),
          axios.get<DrugCategory[]>('http://localhost:8000/drug-categories/')
        ]);
        setBranches(branchesRes.data);
        setRegions(regionsRes.data);
        setClinics(clinicsRes.data);
        setCategories(categoriesRes.data);
      } catch (error) {
        console.error('Error fetching initial data:', error);
        setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });
      }
    };
    fetchData();
  }, [t]);

  // Fetch comparison and trend data when filters change
  const fetchAllData = async () => {
    if (selectedIds.length === 0) {
      setComparisonData([]);
      setTrendData([]);
      return;
    }
    setLoading(true);
    try {
      const [comparisonRes, trendRes] = await Promise.all([
        fetchComparisonData(),
        fetchTrendData(),
      ]);
      // Process and set data
      const processedData = processComparisonData(comparisonRes || []);
      setComparisonData(processedData);
      setTrendData(trendRes || []);

    } catch (error) {
      console.error('Error fetching report data:', error);
      setSnackbar({ open: true, message: t('fetchDataError'), severity: 'error' });
      setComparisonData([]);
      setTrendData([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchComparisonData = async () => {
    const url = `http://localhost:8000/reports/comparison/${compareEntityType === 'branch' ? 'branches' : compareEntityType + 's'}`;
    const idsParam = `${compareEntityType}_ids=${selectedIds.join(',')}`;
    const startDateParam = filterStartDate ? `start_month=${filterStartDate.format('YYYY-MM')}` : '';
    const endDateParam = filterEndDate ? `end_month=${filterEndDate.format('YYYY-MM')}` : '';
    const categoryParam = filterCategoryId ? `category_id=${filterCategoryId}` : '';
    const queryParams = [idsParam, startDateParam, endDateParam, categoryParam].filter(Boolean).join('&');
    const fullUrl = `${url}?${queryParams}`;
    const response = await axios.get<ComparisonDispensedDrugData[]>(fullUrl);
    return response.data;
  };

  const fetchTrendData = async () => {
    const url = 'http://localhost:8000/reports/comparison/trends';
    const entityTypeParam = `entity_type=${compareEntityType}`;
    const entityIdsParam = `entity_ids=${selectedIds.join(',')}`;
    const startDateParam = filterStartDate ? `start_month=${filterStartDate.format('YYYY-MM')}` : '';
    const endDateParam = filterEndDate ? `end_month=${filterEndDate.format('YYYY-MM')}` : '';
    const categoryParam = filterCategoryId ? `category_id=${filterCategoryId}` : '';
    const queryParams = [entityTypeParam, entityIdsParam, startDateParam, endDateParam, categoryParam].filter(Boolean).join('&');
    const fullUrl = `${url}?${queryParams}`;
    const response = await axios.get<ComparisonTrendData[]>(fullUrl);
    return response.data.map((item, index) => ({ ...item, id: item.Month || index.toString() }));
  };

  const processComparisonData = (data: Omit<ComparisonDispensedDrugData, 'id'>[]): ComparisonDispensedDrugData[] => {
    return data.map((item, index) => ({ ...item, id: `${item.EntityID}-${item.DrugID}-${index}` }));
  };

  const handleResetFilters = () => {
    setSelectedIds([]);
    setFilterCategoryId('');
    setFilterStartDate(dayjs().subtract(1, 'year'));
    setFilterEndDate(dayjs());
  };

  const kpiData = useMemo(() => {
    return comparisonData.reduce((acc, item) => {
      acc.totalCost += item.TotalCost;
      acc.totalQuantity += item.TotalQuantity;
      acc.totalCases += item.NumberOfCases;
      return acc;
    }, { totalCost: 0, totalQuantity: 0, totalCases: 0 });
  }, [comparisonData]);

  const categoryDistribution = useMemo(() => {
    const data = comparisonData.reduce((acc, item) => {
      const category = item.CategoryName;
      if (!acc[category]) {
        acc[category] = { name: category, value: 0 };
      }
      acc[category].value += item.TotalCost;
      return acc;
    }, {} as { [key: string]: { name: string, value: number } });
    return Object.values(data);
  }, [comparisonData]);

  const entityComparisonData = useMemo(() => {
    const data = comparisonData.reduce((acc, item) => {
        const entity = item.EntityName;
        if (!acc[entity]) {
            acc[entity] = { name: entity, TotalCost: 0, TotalQuantity: 0, NumberOfCases: 0 };
        }
        acc[entity].TotalCost += item.TotalCost;
        acc[entity].TotalQuantity += item.TotalQuantity;
        acc[entity].NumberOfCases += item.NumberOfCases;
        return acc;
    }, {} as { [key: string]: { name: string, TotalCost: number, TotalQuantity: number, NumberOfCases: number } });
    return Object.values(data);
}, [comparisonData]);

  const renderEntitySelection = () => {
    const items = compareEntityType === 'clinic' ? clinics : compareEntityType === 'region' ? regions : branches;
    const label = t(`select${compareEntityType.charAt(0).toUpperCase() + compareEntityType.slice(1)}s`);
    return (
      <FormControl sx={{ minWidth: 240, maxWidth: 400 }}>
        <InputLabel>{label}</InputLabel>
        <Select
          multiple
          value={selectedIds}
          onChange={(e) => setSelectedIds(e.target.value as number[])}
          input={<OutlinedInput label={label} />}
          renderValue={(selected) => selected.map(id => items.find(i => i.id === id)?.name).join(', ')}
        >
          {items.map((item) => (
            <MenuItem key={item.id} value={item.id}>
              <Checkbox checked={selectedIds.indexOf(item.id) > -1} />
              <ListItemText primary={item.name} />
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    );
  };

  const columns: GridColDef[] = [
    { field: 'EntityName', headerName: t('entityName'), width: 180 },
    { field: 'CategoryName', headerName: t('category'), width: 150 },
    { field: 'DrugName', headerName: t('drugName'), width: 200 },
    { field: 'TotalQuantity', headerName: t('totalQuantity'), type: 'number', width: 130 },
    { field: 'TotalCost', headerName: t('totalCost'), type: 'number', width: 130, valueFormatter: (params: { value: number }) => params.value.toFixed(2) },
    { field: 'NumberOfCases', headerName: t('numberOfCases'), type: 'number', width: 130 },
  ];

  const trendColumns: GridColDef[] = [
    { field: 'Month', headerName: t('month'), width: 150 },
    { field: 'TotalQuantity', headerName: t('totalQuantity'), type: 'number', width: 150 },
    { field: 'TotalCost', headerName: t('totalCost'), type: 'number', width: 150, valueFormatter: (params: { value: number }) => params.value.toFixed(2) },
    { field: 'NumberOfCases', headerName: t('numberOfCases'), type: 'number', width: 150 },
  ];

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#AF19FF', '#FF1919'];

  const handleExportPdf = async () => {
    if (comparisonData.length === 0) {
      setSnackbar({ open: true, message: t('noDataToExport'), severity: 'error' });
      return;
    }

    try {
      const reportTitle = t('comparisonDashboard');
      const filename = `تقرير-المقارنة-${dayjs().format('YYYY-MM-DD')}.pdf`;

      await exportComparisonReportToPdf('comparison-report-content', filename, reportTitle);
      setSnackbar({ open: true, message: t('pdfExportSuccess'), severity: 'success' });
    } catch (error) {
      console.error('Error exporting PDF:', error);
      setSnackbar({ open: true, message: t('pdfExportError'), severity: 'error' });
    }
  };

  const handleExportExcel = async () => {
    if (comparisonData.length === 0) {
      setSnackbar({ open: true, message: t('noDataToExport'), severity: 'error' });
      return;
    }

    try {
      const reportTitle = t('comparisonDashboard');
      const filename = `تقرير-المقارنة-${dayjs().format('YYYY-MM-DD')}.xlsx`;

      await exportComparisonReportToExcel(comparisonData, trendData, kpiData, filename, reportTitle);
      setSnackbar({ open: true, message: 'تم تصدير ملف Excel بنجاح', severity: 'success' });
    } catch (error) {
      console.error('Error exporting Excel:', error);
      setSnackbar({ open: true, message: 'خطأ في تصدير ملف Excel', severity: 'error' });
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ p: 3 }} id="comparison-report-content"> {/* Added ID here */}
        <Typography variant="h4" gutterBottom>{t('comparisonDashboard')}</Typography>
        
        {/* --- FILTERS --- */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={3}><FormControl fullWidth><InputLabel>{t('compareBy')}</InputLabel><Select value={compareEntityType} label={t('compareBy')} onChange={(e) => { setCompareEntityType(e.target.value as any); setSelectedIds([]); }}>
              <MenuItem value="clinic">{t('clinics')}</MenuItem>
              <MenuItem value="region">{t('regions')}</MenuItem>
              <MenuItem value="branch">{t('branches')}</MenuItem>
            </Select></FormControl></Grid>
            <Grid item xs={12} sm={6} md={3}>{renderEntitySelection()}</Grid>
            <Grid item xs={12} sm={6} md={3}><FormControl fullWidth><InputLabel>{t('filterByCategory')}</InputLabel><Select value={filterCategoryId} label={t('filterByCategory')} onChange={(e) => setFilterCategoryId(e.target.value as number)}>
              <MenuItem value="">{t('allCategories')}</MenuItem>
              {categories.map((cat) => <MenuItem key={cat.CategoryID} value={cat.CategoryID}>{cat.CategoryName}</MenuItem>)}
            </Select></FormControl></Grid>
            <Grid item xs={12} sm={6} md={3}><DatePicker label={t('filterByStartMonth')} views={['year', 'month']} openTo="month" value={filterStartDate} onChange={setFilterStartDate} slots={{ textField: TextField }} slotProps={{ textField: { fullWidth: true } }} /></Grid>
            <Grid item xs={12} sm={6} md={3}><DatePicker label={t('filterByEndMonth')} views={['year', 'month']} openTo="month" value={filterEndDate} onChange={setFilterEndDate} slots={{ textField: TextField }} slotProps={{ textField: { fullWidth: true } }} /></Grid>
            <Grid item xs={12} sm={6} md={3}><Button variant="contained" color="primary" onClick={fetchAllData} disabled={loading || selectedIds.length === 0}>{t('applyFilters')}</Button></Grid>
            <Grid item xs={12} sm={6} md={3}><Button variant="outlined" color="secondary" onClick={handleResetFilters}>{t('resetFilters')}</Button></Grid>
            <Grid item xs={12} sm={6} md={3}><Button variant="contained" color="success" onClick={handleExportPdf} disabled={comparisonData.length === 0}>تصدير PDF</Button></Grid>
            <Grid item xs={12} sm={6} md={3}><Button variant="contained" color="info" onClick={handleExportExcel} disabled={comparisonData.length === 0}>تصدير Excel</Button></Grid>
          </Grid>
        </Paper>

        {/* --- KPIs --- */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={3}><Card><CardContent><Typography color="textSecondary" gutterBottom>{t('totalCost')}</Typography><Typography variant="h5">{kpiData.totalCost.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</Typography></CardContent></Card></Grid>
            <Grid item xs={12} sm={6} md={3}><Card><CardContent><Typography color="textSecondary" gutterBottom>{t('totalQuantity')}</Typography><Typography variant="h5">{kpiData.totalQuantity.toLocaleString()}</Typography></CardContent></Card></Grid>
            <Grid item xs={12} sm={6} md={3}><Card><CardContent><Typography color="textSecondary" gutterBottom>{t('numberOfCases')}</Typography><Typography variant="h5">{kpiData.totalCases.toLocaleString()}</Typography></CardContent></Card></Grid>
            <Grid item xs={12} sm={6} md={3}><Card><CardContent><Typography color="textSecondary" gutterBottom>{t('avgCostPerCase')}</Typography><Typography variant="h5">{(kpiData.totalCases > 0 ? kpiData.totalCost / kpiData.totalCases : 0).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</Typography></CardContent></Card></Grid>
        </Grid>

        {/* --- CHARTS --- */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={8}><Paper sx={{ p: 2, height: 400 }}><Typography variant="h6">{t('monthlyTrends')}</Typography><ResponsiveContainer><BarChart data={trendData}><CartesianGrid strokeDasharray="3 3" /><XAxis dataKey="Month" /><YAxis /><Tooltip /><Legend /><Bar dataKey="TotalCost" fill="#8884d8" name={t('totalCost')} /><Bar dataKey="TotalQuantity" fill="#82ca9d" name={t('totalQuantity')} /></BarChart></ResponsiveContainer></Paper></Grid>
          <Grid item xs={12} md={4}><Paper sx={{ p: 2, height: 400 }}><Typography variant="h6">{t('costByCategory')}</Typography><ResponsiveContainer><PieChart><Pie data={categoryDistribution} dataKey="value" nameKey="name" cx="50%" cy="50%" outerRadius={100} fill="#8884d8" label>{categoryDistribution.map((entry, index) => <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />)}</Pie><Tooltip /></PieChart></ResponsiveContainer></Paper></Grid>
          <Grid item xs={12}><Paper sx={{ p: 2, height: 400 }}><Typography variant="h6">{t('entityComparison')}</Typography><ResponsiveContainer><BarChart data={entityComparisonData}><CartesianGrid strokeDasharray="3 3" /><XAxis dataKey="name" /><YAxis /><Tooltip /><Legend /><Bar dataKey="TotalCost" fill="#82ca9d" name={t('totalCost')} /><Bar dataKey="TotalQuantity" fill="#8884d8" name={t('totalQuantity')} /></BarChart></ResponsiveContainer></Paper></Grid>
        </Grid>

        {/* --- DATA GRID --- */}
        <Paper sx={{ height: 600, width: '100%', mb: 3 }}>
          <Typography variant="h6" sx={{ p: 2 }}>{t('detailedComparisonData')}</Typography>
          <DataGrid
            rows={comparisonData}
            columns={columns}
            loading={loading}
            slots={{ toolbar: GridToolbar }}
            slotProps={{
              toolbar: {
                showQuickFilter: true,
                quickFilterProps: { debounceMs: 500 },
                csvOptions: { disableToolbarButton: false },
                printOptions: { disableToolbarButton: false },
              },
            }}
            initialState={{
              pagination: {
                paginationModel: { pageSize: 100, page: 0 },
              },
            }}
            pageSizeOptions={[10, 50, 100]}
            checkboxSelection
            disableRowSelectionOnClick
          />
        </Paper>

        <Paper sx={{ height: 400, width: '100%' }}>
          <Typography variant="h6" sx={{ p: 2 }}>{t('monthlyTrendData')}</Typography>
          <DataGrid
            rows={trendData}
            columns={trendColumns}
            loading={loading}
            slots={{ toolbar: GridToolbar }}
            slotProps={{
              toolbar: {
                showQuickFilter: true,
                quickFilterProps: { debounceMs: 500 },
                csvOptions: { disableToolbarButton: false },
                printOptions: { disableToolbarButton: false },
              },
            }}
            initialState={{
              pagination: {
                paginationModel: { pageSize: 50, page: 0 },
              },
            }}
            pageSizeOptions={[10, 25, 50]}
            disableRowSelectionOnClick
          />
        </Paper>

        {/* Removed the old print button */}
        {/* <Button variant="outlined" onClick={() => window.print()} sx={{ mt: 2 }}>
          {t('printReport')}
        </Button> */}

        {snackbar && <Snackbar open={snackbar.open} autoHideDuration={6000} onClose={() => setSnackbar(null)} anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}><Alert onClose={() => setSnackbar(null)} severity={snackbar.severity} sx={{ width: '100%' }}>{snackbar.message}</Alert></Snackbar>}
      </Box>
    </LocalizationProvider>
  );
};

export default ComparisonReportsPage;