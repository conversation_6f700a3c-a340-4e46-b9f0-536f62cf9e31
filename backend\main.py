import sys
import os
from fastapi import FastAP<PERSON>, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.sql.functions import Extract, func
from datetime import date, datetime, timedelta
from fastapi.responses import StreamingResponse
import logging
from sqlalchemy.dialects import sqlite
import pdfkit

import models, database

# Create all database tables
models.Base.metadata.create_all(bind=database.engine)

app = FastAPI(
    title="Drug Dispensing API",
    description="API for managing and analyzing drug dispensing records.",
    version="1.0.0",
)

origins = [
    "http://localhost:3000",  # Allow your frontend to access the backend
    "http://127.0.0.1:3000",  # Allow access from 127.0.0.1 as well
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Dependency to get the database session
def get_db():
    db = database.SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Helper function to generate HTML for the report
def generate_report_html(data: List[dict], title: str) -> str:
    try:
        html_content = f"""
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>{title}</title>
            <style>
                body {{ font-family: 'Amiri', 'Traditional Arabic', 'Arial', sans-serif; margin: 20mm; font-size: 12pt; }}
                h1 {{ text-align: center; color: #333; font-size: 24pt; margin-bottom: 20px; }}
                table {{ width: 100%; border-collapse: collapse; margin-bottom: 20px; direction: rtl; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; font-family: 'Amiri', 'Traditional Arabic', 'Arial', sans-serif; direction: rtl; unicode-bidi: embed; white-space: normal; }}
                th {{ background-color: #f2f2f2; }}
                .footer {{ text-align: center; font-size: 10pt; color: #777; margin-top: 30px; }}
            </style>
            <!-- Embed Amiri font for Arabic support -->
            <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap" rel="stylesheet">
        </head>
        <body>
            <h1>{title}</h1>
            <table>
                <thead>
                    <tr>
        """
        if data:
            # Generate table headers
            for key in data[0].keys():
                html_content += f"<th>{key}</th>"
            html_content += "</tr></thead><tbody>"

            # Generate table rows
            for row in data:
                html_content += "<tr>"
                for value in row.values():
                    html_content += f"<td>{value}</td>"
                html_content += "</tr>"
        else:
            html_content += "<tr><td>لا توجد بيانات لعرضها.</td></tr>"

        html_content += """
                </tbody>
            </table>
            <div class="footer">
                تقرير تم إنشاؤه بواسطة نظام صرف الأدوية
            </div>
        </body>
        </html>
        """
        return html_content
    except Exception as e:
        logging.error(f"Error generating HTML report: {e}")
        raise HTTPException(status_code=500, detail=f"Error generating PDF report: {e}")

# --- Pydantic Schemas for API data validation ---
from pydantic import BaseModel

# Schemas for Branches
class BranchBase(BaseModel):
    name: str

class BranchCreate(BranchBase):
    pass

class Branch(BranchBase):
    id: int
    
    class Config:
        orm_mode = True

# Schemas for Regions
class RegionBase(BaseModel):
    name: str
    branch_id: int

class RegionCreate(RegionBase):
    pass

class Region(RegionBase):
    id: int

    class Config:
        orm_mode = True

# Schemas for Clinics
class ClinicBase(BaseModel):
    name: str
    region_id: int

class ClinicCreate(ClinicBase):
    pass

class Clinic(ClinicBase):
    id: int

    class Config:
        orm_mode = True


# Schemas for DrugCategory
class DrugCategoryBase(BaseModel):
    CategoryName: str

class DrugCategoryCreate(DrugCategoryBase):
    pass

class DrugCategory(DrugCategoryBase):
    CategoryID: int
    class Config:
        orm_mode = True

# Schemas for Drug
class DrugBase(BaseModel):
    DrugName: str
    CategoryID: int
    Unit: str | None = None

class DrugCreate(DrugBase):
    pass

class Drug(DrugBase):
    DrugID: int
    class Config:
        orm_mode = True

class DrugCreateList(BaseModel):
    drugs: List[DrugCreate]

# Schemas for DispensedDrug
from datetime import date

class DispensedDrugBase(BaseModel):
    ClinicID: int
    DrugID: int
    Quantity: int
    UnitPrice: float
    Cases: int
    DispenseDate: date

class DispensedDrugCreate(DispensedDrugBase):
    pass

class DispensedDrug(DispensedDrugBase):
    DispenseID: int
    TotalCost: float
    CategoryID: int | None = None # Added CategoryID
    class Config:
        orm_mode = True

class DispensedDrugCreateList(BaseModel):
    dispensed_drugs: List[DispensedDrugCreate]

# New Schemas for Aggregated Reports
class AggregatedDispensedDrugClinic(BaseModel):
    Month: Optional[str]
    ClinicID: int
    ClinicName: str
    DrugID: int
    DrugName: str
    DrugUnit: Optional[str]
    CategoryID: int
    CategoryName: str
    UnitPrice: float
    TotalQuantity: float
    TotalCost: float
    NumberOfCases: int

    class Config:
        orm_mode = True

class AggregatedDispensedDrugRegion(BaseModel):
    Month: Optional[str]
    RegionID: int
    RegionName: str
    DrugID: int
    DrugName: str
    DrugUnit: Optional[str]
    CategoryID: int
    CategoryName: str
    UnitPrice: float
    TotalQuantity: float
    TotalCost: float
    NumberOfCases: int

    class Config:
        orm_mode = True

class AggregatedDispensedDrugBranch(BaseModel):
    Month: Optional[str]
    BranchID: int
    BranchName: str
    DrugID: int
    DrugName: str
    DrugUnit: Optional[str]
    CategoryID: int
    CategoryName: str
    UnitPrice: float
    TotalQuantity: float
    TotalCost: float
    NumberOfCases: int

    class Config:
        orm_mode = True

class ComparisonDispensedDrugData(BaseModel):
    Month: Optional[str]
    EntityID: int
    EntityName: str
    DrugID: int
    DrugName: str
    DrugUnit: Optional[str]
    CategoryID: int
    CategoryName: str
    TotalQuantity: float
    TotalCost: float
    NumberOfCases: int

    class Config:
        orm_mode = True

class ComparisonTrendData(BaseModel):
    Month: str
    TotalQuantity: float
    TotalCost: float
    NumberOfCases: int

    class Config:
        orm_mode = True

# Schemas for InsulinType
class InsulinTypeBase(BaseModel):
    InsulinName: str
    SupportType: str | None = None
    Unit: str | None = None
    Balance: int | None = None

class InsulinTypeCreate(InsulinTypeBase):
    pass

class InsulinType(InsulinTypeBase):
    InsulinID: int
    class Config:
        orm_mode = True

# Schemas for DispensedInsulin
class DispensedInsulinBase(BaseModel):
    ClinicID: int
    InsulinID: int
    Quantity: int
    Cases: int
    UnitPrice: float
    Date: date

class DispensedInsulinCreate(DispensedInsulinBase):
    pass

class DispensedInsulin(DispensedInsulinBase):
    ID: int
    TotalCost: float
    class Config:
        orm_mode = True

# Schemas for MonthlyDispense
class MonthlyDispenseBase(BaseModel):
    ClinicID: int
    Year: int
    Month: int
    Hيئة: float | None = None
    طلاب: float | None = None
    رضع: float | None = None
    امرأة_معيلة: float | None = None

class MonthlyDispenseCreate(MonthlyDispenseBase):
    pass

class MonthlyDispense(MonthlyDispenseBase):
    ID: int
    class Config:
        orm_mode = True

# Schemas for MonthlySupplies
class MonthlySuppliesBase(BaseModel):
    ClinicID: int
    Year: int
    Month: int
    مستلزمات_عامة: float | None = None
    أشعة: float | None = None
    معامل: float | None = None
    مستلزمات_عيادات: float | None = None

class MonthlySuppliesCreate(MonthlySuppliesBase):
    pass

class MonthlySupplies(MonthlySuppliesBase):
    ID: int
    class Config:
        orm_mode = True

# Schemas for MedicalTicket
class MedicalTicketBase(BaseModel):
    ClinicID: int
    Year: int
    Month: int
    Week: int # 1-4
    فئة: str # "هيئة" – "طلاب" – "رضع" – "امرأة معيلة"
    تذاكر_56ب: int | None = None
    تذاكر_56ج: int | None = None

class MedicalTicketCreate(MedicalTicketBase):
    pass

class MedicalTicket(MedicalTicketBase):
    TicketID: int
    class Config:
        orm_mode = True

# Schemas for SubsidizedPharmacyTicket
class SubsidizedPharmacyTicketBase(BaseModel):
    ClinicID: int
    Year: int
    Month: int
    Week: int
    هيئة: int | None = None
    طلاب: int | None = None
    مدعّم: int | None = None

class SubsidizedPharmacyTicketCreate(SubsidizedPharmacyTicketBase):
    pass

class SubsidizedPharmacyTicket(SubsidizedPharmacyTicketBase):
    ID: int
    class Config:
        orm_mode = True

# Schemas for CourtRulingsDrug
class CourtRulingsDrugBase(BaseModel):
    BranchID: int
    جهة: str | None = None
    اسم_المريض: str | None = None
    التشخيص: str | None = None
    صنف_العلاج: str | None = None
    الوحدة: str | None = None
    سعر_الوحدة: float | None = None
    الجرعة_شهرياً: int | None = None
    تاريخ_الحكم: date | None = None
    تاريخ_أول_صرف: date | None = None
    شهر_الصرف: date | None = None

class CourtRulingsDrugCreate(CourtRulingsDrugBase):
    pass

class CourtRulingsDrug(CourtRulingsDrugBase):
    ID: int
    التكلفة_شهرياً: float | None = None
    class Config:
        orm_mode = True


# --- API Endpoints ---

@app.get("/")
def read_root():
    return {"message": "Welcome to the Drug Dispensing API"}

# Branch Endpoints
@app.post("/branches/", response_model=Branch, status_code=201)
def create_branch(branch: BranchCreate, db: Session = Depends(get_db)):
    db_branch = models.Branch(**branch.dict())
    db.add(db_branch)
    db.commit()
    db.refresh(db_branch)
    return db_branch

@app.get("/branches/", response_model=List[Branch])
def read_branches(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    branches = db.query(models.Branch).offset(skip).limit(limit).all()
    return branches

@app.get("/branches/{branch_id}", response_model=Branch)
def read_branch(branch_id: int, db: Session = Depends(get_db)):
    db_branch = db.query(models.Branch).filter(models.Branch.id == branch_id).first()
    if db_branch is None:
        raise HTTPException(status_code=404, detail="Branch not found")
    return db_branch

@app.put("/branches/{branch_id}", response_model=Branch)
def update_branch(branch_id: int, branch: BranchCreate, db: Session = Depends(get_db)):
    db_branch = db.query(models.Branch).filter(models.Branch.id == branch_id).first()
    if db_branch is None:
        raise HTTPException(status_code=404, detail="Branch not found")
    db_branch.name = branch.name
    db.commit()
    db.refresh(db_branch)
    return db_branch

@app.delete("/branches/{branch_id}", status_code=204)
def delete_branch(branch_id: int, db: Session = Depends(get_db)):
    db_branch = db.query(models.Branch).filter(models.Branch.id == branch_id).first()
    if db_branch is None:
        raise HTTPException(status_code=404, detail="Branch not found")
    db.delete(db_branch)
    db.commit()
    return {"message": "Branch deleted successfully"}

# Region Endpoints
@app.post("/regions/", response_model=Region, status_code=201)
def create_region(region: RegionCreate, db: Session = Depends(get_db)):
    db_region = models.Region(**region.dict())
    db.add(db_region)
    db.commit()
    db.refresh(db_region)
    return db_region

@app.put("/regions/{region_id}", response_model=Region)
def update_region(region_id: int, region: RegionCreate, db: Session = Depends(get_db)):
    db_region = db.query(models.Region).filter(models.Region.id == region_id).first()
    if db_region is None:
        raise HTTPException(status_code=404, detail="Region not found")
    db_region.name = region.name
    db_region.branch_id = region.branch_id
    db.commit()
    db.refresh(db_region)
    return db_region

@app.get("/regions/", response_model=List[Region])
def read_regions(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    regions = db.query(models.Region).offset(skip).limit(limit).all()
    return regions

@app.delete("/regions/{region_id}", status_code=204)
def delete_region(region_id: int, db: Session = Depends(get_db)):
    db_region = db.query(models.Region).filter(models.Region.id == region_id).first()
    if db_region is None:
        raise HTTPException(status_code=404, detail="Region not found")
    db.delete(db_region)
    db.commit()
    return {"message": "Region deleted successfully"}

# Clinic Endpoints
@app.post("/clinics/", response_model=Clinic, status_code=201)
def create_clinic(clinic: ClinicCreate, db: Session = Depends(get_db)):
    db_clinic = models.Clinic(**clinic.dict())
    db.add(db_clinic)
    db.commit()
    db.refresh(db_clinic)
    return db_clinic

@app.get("/clinics/", response_model=List[Clinic])
def read_clinics(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    clinics = db.query(models.Clinic).offset(skip).limit(limit).all()
    return clinics

@app.get("/clinics/{clinic_id}", response_model=Clinic)
def read_clinic(clinic_id: int, db: Session = Depends(get_db)):
    db_clinic = db.query(models.Clinic).filter(models.Clinic.id == clinic_id).first()
    if db_clinic is None:
        raise HTTPException(status_code=404, detail="Clinic not found")
    return db_clinic

@app.put("/clinics/{clinic_id}", response_model=Clinic)
def update_clinic(clinic_id: int, clinic: ClinicCreate, db: Session = Depends(get_db)):
    db_clinic = db.query(models.Clinic).filter(models.Clinic.id == clinic_id).first()
    if db_clinic is None:
        raise HTTPException(status_code=404, detail="Clinic not found")
    db_clinic.name = clinic.name
    db_clinic.region_id = clinic.region_id
    db.commit()
    db.refresh(db_clinic)
    return db_clinic

@app.delete("/clinics/{clinic_id}", status_code=204)
def delete_clinic(clinic_id: int, db: Session = Depends(get_db)):
    db_clinic = db.query(models.Clinic).filter(models.Clinic.id == clinic_id).first()
    if db_clinic is None:
        raise HTTPException(status_code=404, detail="Clinic not found")
    db.delete(db_clinic)
    db.commit()
    return {"message": "Clinic deleted successfully"}

# DrugCategory Endpoints
@app.post("/drug-categories/", response_model=DrugCategory, status_code=201)
def create_drug_category(category: DrugCategoryCreate, db: Session = Depends(get_db)):
    db_category = models.DrugCategory(**category.dict())
    db.add(db_category)
    db.commit()
    db.refresh(db_category)
    return db_category

@app.get("/drug-categories/", response_model=List[DrugCategory])
def read_drug_categories(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    categories = db.query(models.DrugCategory).offset(skip).limit(limit).all()
    return categories

@app.put("/drug-categories/{category_id}", response_model=DrugCategory)
def update_drug_category(category_id: int, category: DrugCategoryCreate, db: Session = Depends(get_db)):
    db_category = db.query(models.DrugCategory).filter(models.DrugCategory.CategoryID == category_id).first()
    if db_category is None:
        raise HTTPException(status_code=404, detail="Category not found")
    db_category.CategoryName = category.CategoryName
    db.commit()
    db.refresh(db_category)
    return db_category

@app.delete("/drug-categories/{category_id}", status_code=204)
def delete_drug_category(category_id: int, db: Session = Depends(get_db)):
    db_category = db.query(models.DrugCategory).filter(models.DrugCategory.CategoryID == category_id).first()
    if db_category is None:
        raise HTTPException(status_code=404, detail="Category not found")
    db.delete(db_category)
    db.commit()
    return {"message": "Category deleted successfully"}

# Drug Endpoints
@app.post("/drugs/", response_model=Drug, status_code=201)
def create_drug(drug: DrugCreate, db: Session = Depends(get_db)):
    db_drug = models.Drug(**drug.dict())
    db.add(db_drug)
    db.commit()
    db.refresh(db_drug)
    return db_drug

@app.post("/drugs/bulk/", response_model=List[Drug], status_code=201)
def create_bulk_drugs(drug_list: DrugCreateList, db: Session = Depends(get_db)):
    created_drugs = []
    for drug_data in drug_list.drugs:
        db_drug = models.Drug(**drug_data.dict())
        db.add(db_drug)
        created_drugs.append(db_drug)
    db.commit()
    for drug in created_drugs:
        db.refresh(drug)
    return created_drugs

@app.get("/drugs/", response_model=List[Drug])
def read_drugs(skip: int = 0, limit: int = 100, category_id: int | None = None, search: str | None = None, db: Session = Depends(get_db)):
    query = db.query(models.Drug)
    if category_id:
        query = query.filter(models.Drug.CategoryID == category_id)
    if search:
        query = query.filter(models.Drug.DrugName.ilike(f"%{search}%"))
    drugs = query.offset(skip).limit(limit).all()
    return drugs

@app.put("/drugs/{drug_id}", response_model=Drug)
def update_drug(drug_id: int, drug: DrugCreate, db: Session = Depends(get_db)):
    db_drug = db.query(models.Drug).filter(models.Drug.DrugID == drug_id).first()
    if db_drug is None:
        raise HTTPException(status_code=404, detail="Drug not found")
    db_drug.DrugName = drug.DrugName
    db_drug.CategoryID = drug.CategoryID
    db_drug.Unit = drug.Unit
    db.commit()
    db.refresh(db_drug)
    return db_drug

@app.delete("/drugs/{drug_id}", status_code=204)
def delete_drug(drug_id: int, db: Session = Depends(get_db)):
    db_drug = db.query(models.Drug).filter(models.Drug.DrugID == drug_id).first()
    if db_drug is None:
        raise HTTPException(status_code=404, detail="Drug not found")
    db.delete(db_drug)
    db.commit()
    return {"message": "Drug deleted successfully"}

# DispensedDrug Endpoints
@app.post("/dispensed-drugs/", response_model=DispensedDrug, status_code=201)
def create_dispensed_drug(dispensed_drug: DispensedDrugCreate, db: Session = Depends(get_db)):
    # Check for existing entry with same ClinicID, DrugID, Quantity, UnitPrice, and month/year
    existing_dispense = db.query(models.DispensedDrug).filter(
        models.DispensedDrug.ClinicID == dispensed_drug.ClinicID,
        models.DispensedDrug.DrugID == dispensed_drug.DrugID,
        models.DispensedDrug.Quantity == dispensed_drug.Quantity,
        models.DispensedDrug.UnitPrice == dispensed_drug.UnitPrice,
        Extract('year', models.DispensedDrug.DispenseDate) == dispensed_drug.DispenseDate.year,
        Extract('month', models.DispensedDrug.DispenseDate) == dispensed_drug.DispenseDate.month
    ).first()

    if existing_dispense:
        raise HTTPException(status_code=400, detail="Duplicate dispense entry for this clinic, drug, quantity, unit price, and month.")

    db_dispensed_drug = models.DispensedDrug(**dispensed_drug.dict())
    db.add(db_dispensed_drug)
    db.commit()
    db.refresh(db_dispensed_drug)
    return db_dispensed_drug

@app.post("/dispensed-drugs/bulk/", response_model=List[DispensedDrug], status_code=201)
def create_bulk_dispensed_drugs(dispensed_drug_list: DispensedDrugCreateList, db: Session = Depends(get_db)):
    created_dispensed_drugs = []
    for dispensed_drug_data in dispensed_drug_list.dispensed_drugs:
        # Check for existing entry with same ClinicID, DrugID, Quantity, UnitPrice, and month/year
        existing_dispense = db.query(models.DispensedDrug).filter(
            models.DispensedDrug.ClinicID == dispensed_drug_data.ClinicID,
            models.DispensedDrug.DrugID == dispensed_drug_data.DrugID,
            models.DispensedDrug.Quantity == dispensed_drug_data.Quantity,
            models.DispensedDrug.UnitPrice == dispensed_drug_data.UnitPrice,
            Extract('year', models.DispensedDrug.DispenseDate) == dispensed_drug_data.DispenseDate.year,
            Extract('month', models.DispensedDrug.DispenseDate) == dispensed_drug_data.DispenseDate.month
        ).first()

        if existing_dispense:
            raise HTTPException(status_code=400, detail=f"Duplicate dispense entry for drug {dispensed_drug_data.DrugID} in clinic {dispensed_drug_data.ClinicID} for the specified month, quantity, and unit price.")

        db_dispensed_drug = models.DispensedDrug(**dispensed_drug_data.dict())
        db.add(db_dispensed_drug)
        created_dispensed_drugs.append(db_dispensed_drug)
    db.commit()
    for drug in created_dispensed_drugs:
        db.refresh(drug)
    return created_dispensed_drugs

@app.get("/dispensed-drugs/", response_model=List[DispensedDrug])
def read_dispensed_drugs(
    skip: int = 0,
    limit: int = 100,
    clinic_id: int | None = None,
    drug_id: int | None = None,
    category_id: int | None = None,
    month: str | None = None,
    search: str | None = None,
    region_id: int | None = None,  # Added region_id parameter
    db: Session = Depends(get_db)
):
    query = db.query(
        models.DispensedDrug,
        models.Clinic.name.label("ClinicName"),
        models.Drug.DrugName.label("DrugName"),
        models.Drug.Unit.label("DrugUnit"),
        models.DrugCategory.CategoryName.label("CategoryName"),
        models.Drug.CategoryID.label("CategoryID")
    ) \
        .join(models.Clinic, models.DispensedDrug.ClinicID == models.Clinic.id) \
        .join(models.Drug, models.DispensedDrug.DrugID == models.Drug.DrugID) \
        .join(models.DrugCategory, models.Drug.CategoryID == models.DrugCategory.CategoryID)

    if clinic_id:
        query = query.filter(models.DispensedDrug.ClinicID == clinic_id)
    if drug_id:
        query = query.filter(models.DispensedDrug.DrugID == drug_id)
    if category_id:
        query = query.filter(models.Drug.CategoryID == category_id)
    if month:
        year, mon = map(int, month.split('-'))
        query = query.filter(Extract('year', models.DispensedDrug.DispenseDate) == year,
                             Extract('month', models.DispensedDrug.DispenseDate) == mon)
    if search:
        query = query.filter(models.Drug.DrugName.ilike(f"%{search}%"))
    if region_id:  # Filter by region_id
        query = query.join(models.Region, models.Clinic.region_id == models.Region.id).filter(models.Region.id == region_id)

    dispensed_drugs_with_details = query.offset(skip).limit(limit).all()

    # Manually construct DispensedDrug objects with additional fields
    result = []
    for disp, clinic_name, drug_name, drug_unit, category_name, category_id_from_db in dispensed_drugs_with_details:
        disp_dict = disp.__dict__
        disp_dict["ClinicName"] = clinic_name
        disp_dict["DrugName"] = drug_name
        disp_dict["DrugUnit"] = drug_unit
        disp_dict["CategoryName"] = category_name
        disp_dict["CategoryID"] = category_id_from_db  # Assign CategoryID
        result.append(DispensedDrug(**disp_dict))

    return result

@app.put("/dispensed-drugs/{dispense_id}", response_model=DispensedDrug)
def update_dispensed_drug(dispense_id: int, dispensed_drug: DispensedDrugCreate, db: Session = Depends(get_db)):
    db_dispensed_drug = db.query(models.DispensedDrug).filter(models.DispensedDrug.DispenseID == dispense_id).first()
    if db_dispensed_drug is None:
        raise HTTPException(status_code=404, detail="Dispensed drug not found")
    db_dispensed_drug.ClinicID = dispensed_drug.ClinicID
    db_dispensed_drug.DrugID = dispensed_drug.DrugID
    db_dispensed_drug.Quantity = dispensed_drug.Quantity
    db_dispensed_drug.UnitPrice = dispensed_drug.UnitPrice
    db_dispensed_drug.Cases = dispensed_drug.Cases
    db_dispensed_drug.DispenseDate = dispensed_drug.DispenseDate
    db.commit()
    db.refresh(db_dispensed_drug)
    return db_dispensed_drug

@app.delete("/dispensed-drugs/{dispense_id}", status_code=204)
def delete_dispensed_drug(dispense_id: int, db: Session = Depends(get_db)):
    db_dispensed_drug = db.query(models.DispensedDrug).filter(models.DispensedDrug.DispenseID == dispense_id).first()
    if db_dispensed_drug is None:
        raise HTTPException(status_code=404, detail="Dispensed drug not found")
    db.delete(db_dispensed_drug)
    db.commit()
    return {"message": "Dispensed drug deleted successfully"}

# InsulinType Endpoints
@app.post("/insulin-types/", response_model=InsulinType, status_code=201)
def create_insulin_type(insulin_type: InsulinTypeCreate, db: Session = Depends(get_db)):
    db_insulin_type = models.InsulinType(**insulin_type.dict())
    db.add(db_insulin_type)
    db.commit()
    db.refresh(db_insulin_type)
    return db_insulin_type

@app.get("/insulin-types/", response_model=List[InsulinType])
def read_insulin_types(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    insulin_types = db.query(models.InsulinType).offset(skip).limit(limit).all()
    return insulin_types

# DispensedInsulin Endpoints
@app.post("/dispensed-insulin/", response_model=DispensedInsulin, status_code=201)
def create_dispensed_insulin(dispensed_insulin: DispensedInsulinCreate, db: Session = Depends(get_db)):
    db_dispensed_insulin = models.DispensedInsulin(**dispensed_insulin.dict())
    db.add(db_dispensed_insulin)
    db.commit()
    db.refresh(db_dispensed_insulin)
    return db_dispensed_insulin

@app.get("/dispensed-insulin/", response_model=List[DispensedInsulin])
def read_dispensed_insulin(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    dispensed_insulin = db.query(models.DispensedInsulin).offset(skip).limit(limit).all()
    return dispensed_insulin

# MonthlyDispense Endpoints
@app.post("/monthly-dispense/", response_model=MonthlyDispense, status_code=201)
def create_monthly_dispense(monthly_dispense: MonthlyDispenseCreate, db: Session = Depends(get_db)):
    db_monthly_dispense = models.MonthlyDispense(**monthly_dispense.dict())
    db.add(db_monthly_dispense)
    db.commit()
    db.refresh(db_monthly_dispense)
    return db_monthly_dispense

@app.get("/monthly-dispense/", response_model=List[MonthlyDispense])
def read_monthly_dispense(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    monthly_dispense = db.query(models.MonthlyDispense).offset(skip).limit(limit).all()
    return monthly_dispense

# MonthlySupplies Endpoints
@app.post("/monthly-supplies/", response_model=MonthlySupplies, status_code=201)
def create_monthly_supplies(monthly_supplies: MonthlySuppliesCreate, db: Session = Depends(get_db)):
    db_monthly_supplies = models.MonthlySupplies(**monthly_supplies.dict())
    db.add(db_monthly_supplies)
    db.commit()
    db.refresh(db_monthly_supplies)
    return db_monthly_supplies

@app.get("/monthly-supplies/", response_model=List[MonthlySupplies])
def read_monthly_supplies(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    monthly_supplies = db.query(models.MonthlySupplies).offset(skip).limit(limit).all()
    return monthly_supplies

# MedicalTicket Endpoints
@app.post("/medical-tickets/", response_model=MedicalTicket, status_code=201)
def create_medical_ticket(medical_ticket: MedicalTicketCreate, db: Session = Depends(get_db)):
    db_medical_ticket = models.MedicalTicket(**medical_ticket.dict())
    db.add(db_medical_ticket)
    db.commit()
    db.refresh(db_medical_ticket)
    return db_medical_ticket

@app.get("/medical-tickets/", response_model=List[MedicalTicket])
def read_medical_tickets(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    medical_tickets = db.query(models.MedicalTicket).offset(skip).limit(limit).all()
    return medical_tickets

# SubsidizedPharmacyTicket Endpoints
@app.post("/subsidized-pharmacy-tickets/", response_model=SubsidizedPharmacyTicket, status_code=201)
def create_subsidized_pharmacy_ticket(subsidized_pharmacy_ticket: SubsidizedPharmacyTicketCreate, db: Session = Depends(get_db)):
    db_subsidized_pharmacy_ticket = models.SubsidizedPharmacyTicket(**subsidized_pharmacy_ticket.dict())
    db.add(db_subsidized_pharmacy_ticket)
    db.commit()
    db.refresh(db_subsidized_pharmacy_ticket)
    return db_subsidized_pharmacy_ticket

@app.get("/subsidized-pharmacy-tickets/", response_model=List[SubsidizedPharmacyTicket])
def read_subsidized_pharmacy_tickets(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    subsidized_pharmacy_tickets = db.query(models.SubsidizedPharmacyTicket).offset(skip).limit(limit).all()
    return subsidized_pharmacy_tickets

# CourtRulingsDrug Endpoints
@app.post("/court-rulings-drugs/", response_model=CourtRulingsDrug, status_code=201)
def create_court_rulings_drug(court_rulings_drug: CourtRulingsDrugCreate, db: Session = Depends(get_db)):
    db_court_rulings_drug = models.CourtRulingsDrug(**court_rulings_drug.dict())
    db.add(db_court_rulings_drug)
    db.commit()
    db.refresh(db_court_rulings_drug)
    return db_court_rulings_drug

@app.get("/court-rulings-drugs/", response_model=List[CourtRulingsDrug])
def read_court_rulings_drugs(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    court_rulings_drugs = db.query(models.CourtRulingsDrug).offset(skip).limit(limit).all()
    return court_rulings_drugs

# New Report Endpoints
@app.get("/reports/dispensed-drugs/by-clinic", response_model=List[AggregatedDispensedDrugClinic])
def get_aggregated_dispensed_drugs_by_clinic(
    clinic_id: Optional[int] = None,
    region_id: Optional[int] = None,
    branch_id: Optional[int] = None,
    month: Optional[str] = None, # YYYY-MM
    start_month: Optional[str] = None, # YYYY-MM
    end_month: Optional[str] = None, # YYYY-MM
    quarter: Optional[str] = None, # YYYY-Q#
    db: Session = Depends(get_db)
):
    print(f"[Backend] get_aggregated_dispensed_drugs_by_clinic received: clinic_id={clinic_id}, region_id={region_id}, branch_id={branch_id}, month={month}, start_month={start_month}, end_month={end_month}, quarter={quarter}")
    query = db.query(
        func.strftime('%Y-%m', models.DispensedDrug.DispenseDate).label("Month"),
        models.Clinic.id.label("ClinicID"),
        models.Clinic.name.label("ClinicName"),
        models.Drug.DrugID.label("DrugID"),
        models.Drug.DrugName.label("DrugName"),
        models.Drug.Unit.label("DrugUnit"),
        models.DrugCategory.CategoryID.label("CategoryID"),
        models.DrugCategory.CategoryName.label("CategoryName"),
        models.DispensedDrug.UnitPrice.label("UnitPrice"),
        func.sum(models.DispensedDrug.Quantity).label("TotalQuantity"),
        func.sum(models.DispensedDrug.TotalCost).label("TotalCost"),
        func.sum(models.DispensedDrug.Cases).label("NumberOfCases")
    ).join(
        models.Clinic, models.DispensedDrug.ClinicID == models.Clinic.id
    ).join(
        models.Region, models.Clinic.region_id == models.Region.id
    ).join(
        models.Branch, models.Region.branch_id == models.Branch.id
    ).join(
        models.Drug, models.DispensedDrug.DrugID == models.Drug.DrugID
    ).join(
        models.DrugCategory, models.Drug.CategoryID == models.DrugCategory.CategoryID
    )

    if clinic_id:
        query = query.filter(models.Clinic.id == clinic_id)
    if region_id:
        query = query.filter(models.Region.id == region_id)
    if branch_id:
        query = query.filter(models.Branch.id == branch_id)
    if month:
        try:
            year, mon = map(int, month.split('-'))
            query = query.filter(
                Extract('year', models.DispensedDrug.DispenseDate) == year,
                Extract('month', models.DispensedDrug.DispenseDate) == mon
            )
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid month format. Use YYYY-MM.")

    if start_month and end_month:
        try:
            start_year, start_mon = map(int, start_month.split('-'))
            end_year, end_mon = map(int, end_month.split('-'))
            start_date_obj = date(start_year, start_mon, 1)
            # Calculate the last day of the end month
            if end_mon == 12:
                end_date_obj = date(end_year, end_mon, 31)
            else:
                end_date_obj = date(end_year, end_mon + 1, 1) - timedelta(days=1)
            query = query.filter(models.DispensedDrug.DispenseDate.between(start_date_obj, end_date_obj))
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid month format for date range. Use YYYY-MM.")
    elif quarter:
        try:
            year, q = map(int, quarter.split('-Q'))
            start_month_num = (q - 1) * 3 + 1
            end_month_num = q * 3
            query = query.filter(
                Extract('year', models.DispensedDrug.DispenseDate) == year,
                Extract('month', models.DispensedDrug.DispenseDate) >= start_month_num,
                Extract('month', models.DispensedDrug.DispenseDate) <= end_month_num
            )
        except (ValueError, IndexError):
            raise HTTPException(status_code=400, detail="Invalid quarter format. Use YYYY-Q#.")

    query = query.group_by(
        func.strftime('%Y-%m', models.DispensedDrug.DispenseDate),
        models.Clinic.id,
        models.Clinic.name,
        models.Drug.DrugID,
        models.Drug.DrugName,
        models.Drug.Unit,
        models.DrugCategory.CategoryID,
        models.DrugCategory.CategoryName,
        models.DispensedDrug.UnitPrice
    ).order_by(
        func.strftime('%Y-%m', models.DispensedDrug.DispenseDate),
        models.Clinic.name,
        models.Drug.DrugName,
        models.DispensedDrug.UnitPrice
    )

    return query.all()

@app.get("/reports/dispensed-drugs/by-region", response_model=List[AggregatedDispensedDrugRegion])
def get_aggregated_dispensed_drugs_by_region(
    region_id: Optional[int] = None,
    branch_id: Optional[int] = None,
    month: Optional[str] = None, # YYYY-MM
    start_month: Optional[str] = None, # YYYY-MM
    end_month: Optional[str] = None, # YYYY-MM
    quarter: Optional[str] = None, # YYYY-Q#
    db: Session = Depends(get_db)
):
    print(f"[Backend] get_aggregated_dispensed_drugs_by_region received: region_id={region_id}, branch_id={branch_id}, month={month}, start_month={start_month}, end_month={end_month}, quarter={quarter}")
    query = db.query(
        func.strftime('%Y-%m', models.DispensedDrug.DispenseDate).label("Month"),
        models.Region.id.label("RegionID"),
        models.Region.name.label("RegionName"),
        models.Drug.DrugID.label("DrugID"),
        models.Drug.DrugName.label("DrugName"),
        models.Drug.Unit.label("DrugUnit"),
        models.DrugCategory.CategoryID.label("CategoryID"),
        models.DrugCategory.CategoryName.label("CategoryName"),
        models.DispensedDrug.UnitPrice.label("UnitPrice"),
        func.sum(models.DispensedDrug.Quantity).label("TotalQuantity"),
        func.sum(models.DispensedDrug.TotalCost).label("TotalCost"),
        func.sum(models.DispensedDrug.Cases).label("NumberOfCases")
    ).join(
        models.Clinic, models.DispensedDrug.ClinicID == models.Clinic.id
    ).join(
        models.Region, models.Clinic.region_id == models.Region.id
    ).join(
        models.Branch, models.Region.branch_id == models.Branch.id
    ).join(
        models.Drug, models.DispensedDrug.DrugID == models.Drug.DrugID
    ).join(
        models.DrugCategory, models.Drug.CategoryID == models.DrugCategory.CategoryID
    )

    if region_id:
        query = query.filter(models.Region.id == region_id)
    if branch_id:
        query = query.filter(models.Branch.id == branch_id)
    if month:
        try:
            year, mon = map(int, month.split('-'))
            query = query.filter(
                Extract('year', models.DispensedDrug.DispenseDate) == year,
                Extract('month', models.DispensedDrug.DispenseDate) == mon
            )
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid month format. Use YYYY-MM.")

    if start_month and end_month:
        try:
            start_year, start_mon = map(int, start_month.split('-'))
            end_year, end_mon = map(int, end_month.split('-'))
            start_date_obj = date(start_year, start_mon, 1)
            if end_mon == 12:
                end_date_obj = date(end_year, end_mon, 31)
            else:
                end_date_obj = date(end_year, end_mon + 1, 1) - timedelta(days=1)
            query = query.filter(models.DispensedDrug.DispenseDate.between(start_date_obj, end_date_obj))
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid month format for date range. Use YYYY-MM.")
    elif quarter:
        try:
            year, q = map(int, quarter.split('-Q'))
            start_month_num = (q - 1) * 3 + 1
            end_month_num = q * 3
            query = query.filter(
                Extract('year', models.DispensedDrug.DispenseDate) == year,
                Extract('month', models.DispensedDrug.DispenseDate) >= start_month_num,
                Extract('month', models.DispensedDrug.DispenseDate) <= end_month_num
            )
        except (ValueError, IndexError):
            raise HTTPException(status_code=400, detail="Invalid quarter format. Use YYYY-Q#.")

    query = query.group_by(
        func.strftime('%Y-%m', models.DispensedDrug.DispenseDate),
        models.Region.id,
        models.Region.name,
        models.Drug.DrugID,
        models.Drug.DrugName,
        models.Drug.Unit,
        models.DrugCategory.CategoryID,
        models.DrugCategory.CategoryName
    ).order_by(
        func.strftime('%Y-%m', models.DispensedDrug.DispenseDate),
        models.Region.name,
        models.Drug.DrugName
    )

    return query.all()

@app.get("/reports/dispensed-drugs/by-branch", response_model=List[AggregatedDispensedDrugBranch])
def get_aggregated_dispensed_drugs_by_branch(
    branch_id: Optional[int] = None,
    month: Optional[str] = None, # YYYY-MM
    start_month: Optional[str] = None, # YYYY-MM
    end_month: Optional[str] = None, # YYYY-MM
    quarter: Optional[str] = None, # YYYY-Q#
    db: Session = Depends(get_db)
):
    print(f"[Backend] get_aggregated_dispensed_drugs_by_branch received: branch_id={branch_id}, month={month}, start_month={start_month}, end_month={end_month}, quarter={quarter}")
    query = db.query(
        func.strftime('%Y-%m', models.DispensedDrug.DispenseDate).label("Month"),
        models.Branch.id.label("BranchID"),
        models.Branch.name.label("BranchName"),
        models.Drug.DrugID.label("DrugID"),
        models.Drug.DrugName.label("DrugName"),
        models.Drug.Unit.label("DrugUnit"),
        models.DrugCategory.CategoryID.label("CategoryID"),
        models.DrugCategory.CategoryName.label("CategoryName"),
        models.DispensedDrug.UnitPrice.label("UnitPrice"),
        func.sum(models.DispensedDrug.Quantity).label("TotalQuantity"),
        func.sum(models.DispensedDrug.TotalCost).label("TotalCost"),
        func.sum(models.DispensedDrug.Cases).label("NumberOfCases")
    ).join(
        models.Clinic, models.DispensedDrug.ClinicID == models.Clinic.id
    ).join(
        models.Region, models.Clinic.region_id == models.Region.id
    ).join(
        models.Branch, models.Region.branch_id == models.Branch.id
    ).join(
        models.Drug, models.DispensedDrug.DrugID == models.Drug.DrugID
    ).join(
        models.DrugCategory, models.Drug.CategoryID == models.DrugCategory.CategoryID
    )

    if branch_id:
        query = query.filter(models.Branch.id == branch_id)
    if month:
        try:
            year, mon = map(int, month.split('-'))
            query = query.filter(
                Extract('year', models.DispensedDrug.DispenseDate) == year,
                Extract('month', models.DispensedDrug.DispenseDate) == mon
            )
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid month format. Use YYYY-MM.")

    if start_month and end_month:
        try:
            start_year, start_mon = map(int, start_month.split('-'))
            end_year, end_mon = map(int, end_month.split('-'))
            start_date_obj = date(start_year, start_mon, 1)
            if end_mon == 12:
                end_date_obj = date(end_year, end_mon, 31)
            else:
                end_date_obj = date(end_year, end_mon + 1, 1) - timedelta(days=1)
            query = query.filter(models.DispensedDrug.DispenseDate.between(start_date_obj, end_date_obj))
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid month format for date range. Use YYYY-MM.")
    elif quarter:
        try:
            year, q = map(int, quarter.split('-Q'))
            start_month_num = (q - 1) * 3 + 1
            end_month_num = q * 3
            query = query.filter(
                Extract('year', models.DispensedDrug.DispenseDate) == year,
                Extract('month', models.DispensedDrug.DispenseDate) >= start_month_num,
                Extract('month', models.DispensedDrug.DispenseDate) <= end_month_num
            )
        except (ValueError, IndexError):
            raise HTTPException(status_code=400, detail="Invalid quarter format. Use YYYY-Q#.")

    query = query.group_by(
        func.strftime('%Y-%m', models.DispensedDrug.DispenseDate),
        models.Branch.id,
        models.Branch.name,
        models.Drug.DrugID,
        models.Drug.DrugName,
        models.Drug.Unit,
        models.DrugCategory.CategoryID,
        models.DrugCategory.CategoryName
    ).order_by(
        func.strftime('%Y-%m', models.DispensedDrug.DispenseDate),
        models.Branch.name,
        models.Drug.DrugName
    )

    return query.all()

@app.get("/reports/comparison/clinics", response_model=List[ComparisonDispensedDrugData])
def get_comparison_dispensed_drugs_by_clinics(
    clinic_ids: str = Depends(lambda clinic_ids: clinic_ids),
    category_id: Optional[int] = None,
    drug_id: Optional[int] = None,
    month: Optional[str] = None, # YYYY-MM
    start_month: Optional[str] = None, # YYYY-MM
    end_month: Optional[str] = None, # YYYY-MM
    quarter: Optional[str] = None, # YYYY-Q#
    db: Session = Depends(get_db)
):
    print(f"[Backend] get_comparison_dispensed_drugs_by_clinics received: clinic_ids={clinic_ids}, category_id={category_id}, drug_id={drug_id}, month={month}, start_month={start_month}, end_month={end_month}, quarter={quarter}")
    query = db.query(
        func.strftime('%Y-%m', models.DispensedDrug.DispenseDate).label("Month"),
        models.Clinic.id.label("EntityID"),
        models.Clinic.name.label("EntityName"),
        models.Drug.DrugID.label("DrugID"),
        models.Drug.DrugName.label("DrugName"),
        models.Drug.Unit.label("DrugUnit"),
        models.DrugCategory.CategoryID.label("CategoryID"),
        models.DrugCategory.CategoryName.label("CategoryName"),
        func.sum(models.DispensedDrug.Quantity).label("TotalQuantity"),
        func.sum(models.DispensedDrug.TotalCost).label("TotalCost"),
        func.sum(models.DispensedDrug.Cases).label("NumberOfCases")
    ).join(
        models.Clinic, models.DispensedDrug.ClinicID == models.Clinic.id
    ).join(
        models.Drug, models.DispensedDrug.DrugID == models.Drug.DrugID
    ).join(
        models.DrugCategory, models.Drug.CategoryID == models.DrugCategory.CategoryID
    )

    if clinic_ids:
        clinic_ids_list = [int(cid) for cid in clinic_ids.split(',')]
        query = query.filter(models.Clinic.id.in_(clinic_ids_list))
    if category_id:
        query = query.filter(models.DrugCategory.CategoryID == category_id)
    if drug_id:
        query = query.filter(models.Drug.DrugID == drug_id)
    if month:
        try:
            year, mon = map(int, month.split('-'))
            query = query.filter(
                Extract('year', models.DispensedDrug.DispenseDate) == year,
                Extract('month', models.DispensedDrug.DispenseDate) == mon
            )
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid month format. Use YYYY-MM.")

    if start_month and end_month:
        try:
            start_year, start_mon = map(int, start_month.split('-'))
            end_year, end_mon = map(int, end_month.split('-'))
            start_date_obj = date(start_year, start_mon, 1)
            if end_mon == 12:
                end_date_obj = date(end_year, end_mon, 31)
            else:
                end_date_obj = date(end_year, end_mon + 1, 1) - timedelta(days=1)
            query = query.filter(models.DispensedDrug.DispenseDate.between(start_date_obj, end_date_obj))
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid month format for date range. Use YYYY-MM.")
    elif quarter:
        try:
            year, q = map(int, quarter.split('-Q'))
            start_month_num = (q - 1) * 3 + 1
            end_month_num = q * 3
            query = query.filter(
                Extract('year', models.DispensedDrug.DispenseDate) == year,
                Extract('month', models.DispensedDrug.DispenseDate) >= start_month_num,
                Extract('month', models.DispensedDrug.DispenseDate) <= end_month_num
            )
        except (ValueError, IndexError):
            raise HTTPException(status_code=400, detail="Invalid quarter format. Use YYYY-Q#.")

    query = query.group_by(
        func.strftime('%Y-%m', models.DispensedDrug.DispenseDate),
        models.Clinic.id,
        models.Clinic.name,
        models.Drug.DrugID,
        models.Drug.DrugName,
        models.Drug.Unit,
        models.DrugCategory.CategoryID,
        models.DrugCategory.CategoryName
    ).order_by(
        func.strftime('%Y-%m', models.DispensedDrug.DispenseDate),
        models.Clinic.name,
        models.Drug.DrugName
    )

    # --- DEBUGGING: Log the generated SQL query ---
    sql_query = query.statement.compile(dialect=sqlite.dialect(), compile_kwargs={"literal_binds": True})
    logging.warning(f"--- SQL QUERY (Comparison/Clinics) ---\n{sql_query}\n-------------------")

    results = query.all()

    # --- DEBUGGING: Log the results from the database ---
    logging.warning(f"--- DB RESULTS (Comparison/Clinics) ---\n{results}\n-------------------")

    return results

@app.get("/reports/comparison/regions", response_model=List[ComparisonDispensedDrugData])
def get_comparison_dispensed_drugs_by_regions(
    region_ids: str = Depends(lambda region_ids: region_ids),
    category_id: Optional[int] = None,
    drug_id: Optional[int] = None, # New filter for drug ID
    month: Optional[str] = None, # YYYY-MM
    start_month: Optional[str] = None, # YYYY-MM
    end_month: Optional[str] = None, # YYYY-MM
    quarter: Optional[str] = None, # YYYY-Q#
    db: Session = Depends(get_db)
):
    print(f"[Backend] get_comparison_dispensed_drugs_by_regions received: region_ids={region_ids}, category_id={category_id}, drug_id={drug_id}, month={month}, start_month={start_month}, end_month={end_month}, quarter={quarter}")
    query = db.query(
        func.strftime('%Y-%m', models.DispensedDrug.DispenseDate).label("Month"),
        models.Region.id.label("EntityID"),
        models.Region.name.label("EntityName"),
        models.Drug.DrugID.label("DrugID"),
        models.Drug.DrugName.label("DrugName"),
        models.Drug.Unit.label("DrugUnit"),
        models.DrugCategory.CategoryID.label("CategoryID"),
        models.DrugCategory.CategoryName.label("CategoryName"),
        func.sum(models.DispensedDrug.Quantity).label("TotalQuantity"),
        func.sum(models.DispensedDrug.TotalCost).label("TotalCost"),
        func.sum(models.DispensedDrug.Cases).label("NumberOfCases")
    ).join(
        models.Clinic, models.DispensedDrug.ClinicID == models.Clinic.id
    ).join(
        models.Region, models.Clinic.region_id == models.Region.id
    ).join(
        models.Drug, models.DispensedDrug.DrugID == models.Drug.DrugID
    ).join(
        models.DrugCategory, models.Drug.CategoryID == models.DrugCategory.CategoryID
    )

    if region_ids:
        region_ids_list = [int(rid) for rid in region_ids.split(',')]
        query = query.filter(models.Region.id.in_(region_ids_list))
    if category_id:
        query = query.filter(models.DrugCategory.CategoryID == category_id)
    if drug_id:
        query = query.filter(models.Drug.DrugID == drug_id)
    if month:
        try:
            year, mon = map(int, month.split('-'))
            query = query.filter(
                Extract('year', models.DispensedDrug.DispenseDate) == year,
                Extract('month', models.DispensedDrug.DispenseDate) == mon
            )
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid month format. Use YYYY-MM.")

    if start_month and end_month:
        try:
            start_year, start_mon = map(int, start_month.split('-'))
            end_year, end_mon = map(int, end_month.split('-'))
            start_date_obj = date(start_year, start_mon, 1)
            if end_mon == 12:
                end_date_obj = date(end_year, end_mon, 31)
            else:
                end_date_obj = date(end_year, end_mon + 1, 1) - timedelta(days=1)
            query = query.filter(models.DispensedDrug.DispenseDate.between(start_date_obj, end_date_obj))
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid month format for date range. Use YYYY-MM.")
    elif quarter:
        try:
            year, q = map(int, quarter.split('-Q'))
            start_month_num = (q - 1) * 3 + 1
            end_month_num = q * 3
            query = query.filter(
                Extract('year', models.DispensedDrug.DispenseDate) == year,
                Extract('month', models.DispensedDrug.DispenseDate) >= start_month_num,
                Extract('month', models.DispensedDrug.DispenseDate) <= end_month_num
            )
        except (ValueError, IndexError):
            raise HTTPException(status_code=400, detail="Invalid quarter format. Use YYYY-Q#.")

    query = query.group_by(
        func.strftime('%Y-%m', models.DispensedDrug.DispenseDate),
        models.Region.id,
        models.Region.name,
        models.Drug.DrugID,
        models.Drug.DrugName,
        models.Drug.Unit,
        models.DrugCategory.CategoryID,
        models.DrugCategory.CategoryName
    ).order_by(
        func.strftime('%Y-%m', models.DispensedDrug.DispenseDate),
        models.Region.name,
        models.Drug.DrugName
    )

    return query.all()

@app.get("/reports/comparison/branches", response_model=List[ComparisonDispensedDrugData])
def get_comparison_dispensed_drugs_by_branches(
    branch_ids: str = Depends(lambda branch_ids: branch_ids),
    category_id: Optional[int] = None,
    drug_id: Optional[int] = None, # New filter for drug ID
    month: Optional[str] = None, # YYYY-MM
    start_month: Optional[str] = None, # YYYY-MM
    end_month: Optional[str] = None, # YYYY-MM
    quarter: Optional[str] = None, # YYYY-Q#
    db: Session = Depends(get_db)
):
    print(f"[Backend] get_comparison_dispensed_drugs_by_branches received: branch_ids={branch_ids}, category_id={category_id}, month={month}, start_month={start_month}, end_month={end_month}, quarter={quarter}")
    query = db.query(
        func.strftime('%Y-%m', models.DispensedDrug.DispenseDate).label("Month"),
        models.Branch.id.label("EntityID"),
        models.Branch.name.label("EntityName"),
        models.Drug.DrugID.label("DrugID"),
        models.Drug.DrugName.label("DrugName"),
        models.Drug.Unit.label("DrugUnit"),
        models.DrugCategory.CategoryID.label("CategoryID"),
        models.DrugCategory.CategoryName.label("CategoryName"),
        func.sum(models.DispensedDrug.Quantity).label("TotalQuantity"),
        func.sum(models.DispensedDrug.TotalCost).label("TotalCost"),
        func.sum(models.DispensedDrug.Cases).label("NumberOfCases")
    ).join(
        models.Clinic, models.DispensedDrug.ClinicID == models.Clinic.id
    ).join(
        models.Region, models.Clinic.region_id == models.Region.id
    ).join(
        models.Branch, models.Region.branch_id == models.Branch.id
    ).join(
        models.Drug, models.DispensedDrug.DrugID == models.Drug.DrugID
    ).join(
        models.DrugCategory, models.Drug.CategoryID == models.DrugCategory.CategoryID
    )

    if branch_ids:
        branch_ids_list = [int(bid) for bid in branch_ids.split(',')]
        query = query.filter(models.Branch.id.in_(branch_ids_list))
    if category_id:
        query = query.filter(models.DrugCategory.CategoryID == category_id)
    if drug_id:
        query = query.filter(models.Drug.DrugID == drug_id)
    if month:
        try:
            year, mon = map(int, month.split('-'))
            query = query.filter(
                Extract('year', models.DispensedDrug.DispenseDate) == year,
                Extract('month', models.DispensedDrug.DispenseDate) == mon
            )
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid month format. Use YYYY-MM.")

    if start_month and end_month:
        try:
            start_year, start_mon = map(int, start_month.split('-'))
            end_year, end_mon = map(int, end_month.split('-'))
            start_date_obj = date(start_year, start_mon, 1)
            if end_mon == 12:
                end_date_obj = date(end_year, end_mon, 31)
            else:
                end_date_obj = date(end_year, end_mon + 1, 1) - timedelta(days=1)
            query = query.filter(models.DispensedDrug.DispenseDate.between(start_date_obj, end_date_obj))
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid month format for date range. Use YYYY-MM.")
    elif quarter:
        try:
            year, q = map(int, quarter.split('-Q'))
            start_month_num = (q - 1) * 3 + 1
            end_month_num = q * 3
            query = query.filter(
                Extract('year', models.DispensedDrug.DispenseDate) == year,
                Extract('month', models.DispensedDrug.DispenseDate) >= start_month_num,
                Extract('month', models.DispensedDrug.DispenseDate) <= end_month_num
            )
        except (ValueError, IndexError):
            raise HTTPException(status_code=400, detail="Invalid quarter format. Use YYYY-Q#.")

    query = query.group_by(
        func.strftime('%Y-%m', models.DispensedDrug.DispenseDate),
        models.Branch.id,
        models.Branch.name,
        models.Drug.DrugID,
        models.Drug.DrugName,
        models.Drug.Unit,
        models.DrugCategory.CategoryID,
        models.DrugCategory.CategoryName
    ).order_by(
        func.strftime('%Y-%m', models.DispensedDrug.DispenseDate),
        models.Branch.name,
        models.Drug.DrugName
    )

    return query.all()

@app.get("/reports/comparison/trends", response_model=List[ComparisonTrendData])
def get_comparison_trends(
    entity_type: str,
    entity_ids: str,
    category_id: Optional[int] = None,
    drug_id: Optional[int] = None, # New filter for drug ID
    start_month: Optional[str] = None, # YYYY-MM
    end_month: Optional[str] = None, # YYYY-MM
    db: Session = Depends(get_db)
):
    try:
        entity_ids_list = [int(eid) for eid in entity_ids.split(',')]
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid entity_ids format. Must be a comma-separated list of integers.")

    query = db.query(
        func.strftime('%Y-%m', models.DispensedDrug.DispenseDate).label("Month"),
        func.sum(models.DispensedDrug.Quantity).label("TotalQuantity"),
        func.sum(models.DispensedDrug.TotalCost).label("TotalCost"),
        func.sum(models.DispensedDrug.Cases).label("NumberOfCases")
    ).join(
        models.Drug, models.DispensedDrug.DrugID == models.Drug.DrugID
    )

    if entity_type == 'clinic':
        query = query.filter(models.DispensedDrug.ClinicID.in_(entity_ids_list))
    elif entity_type == 'region':
        query = query.join(models.Clinic, models.DispensedDrug.ClinicID == models.Clinic.id)\
                     .filter(models.Clinic.region_id.in_(entity_ids_list))
    elif entity_type == 'branch':
        query = query.join(models.Clinic, models.DispensedDrug.ClinicID == models.Clinic.id)\
                     .join(models.Region, models.Clinic.region_id == models.Region.id)\
                     .filter(models.Region.branch_id.in_(entity_ids_list))
    else:
        raise HTTPException(status_code=400, detail="Invalid entity_type. Must be one of: clinic, region, branch.")

    if category_id:
        query = query.filter(models.Drug.CategoryID == category_id)

    if start_month and end_month:
        try:
            start_date_obj = datetime.strptime(start_month, '%Y-%m').date()
            end_year, end_mon = map(int, end_month.split('-'))
            if end_mon == 12:
                end_date_obj = date(end_year, end_mon, 31)
            else:
                end_date_obj = date(end_year, end_mon + 1, 1) - timedelta(days=1)
            query = query.filter(models.DispensedDrug.DispenseDate.between(start_date_obj, end_date_obj))
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid month format for date range. Use YYYY-MM.")

    query = query.group_by(func.strftime('%Y-%m', models.DispensedDrug.DispenseDate)).order_by(func.strftime('%Y-%m', models.DispensedDrug.DispenseDate))

    return query.all()

@app.post("/generate-pdf-report")
async def generate_pdf_report(report_data: List[dict], title: str = "Report"):
    import tempfile
    print(f"[Backend] Received report_data for PDF: {len(report_data)} items")
    print(f"[Backend] Received title for PDF: {title}")
    
    html_file_path = None
    try:
        if not report_data:
            raise HTTPException(status_code=400, detail="No data provided for the report.")

        html_content = generate_report_html(report_data, title)
        
        # Use a temporary file to avoid encoding issues with pdfkit.from_string
        with tempfile.NamedTemporaryFile(delete=False, suffix='.html', mode='w', encoding='utf-8') as temp_f:
            temp_f.write(html_content)
            html_file_path = temp_f.name

        print(f"Generated temporary HTML file: {html_file_path}")

        wkhtmltopdf_path = os.environ.get('WKHTMLTOPDF_PATH', r'C:\Program Files\wkhtmltopdf\bin\wkhtmltopdf.exe')
        if not os.path.exists(wkhtmltopdf_path):
            logging.error(f"wkhtmltopdf not found at path: {wkhtmltopdf_path}")
            raise HTTPException(status_code=500, detail=f"PDF generation failed: 'wkhtmltopdf.exe' not found at the specified path. Please install it or set the WKHTMLTOPDF_PATH environment variable.")

        config = pdfkit.configuration(wkhtmltopdf=wkhtmltopdf_path)
        
        options = {
            'encoding': "UTF-8",
            'no-outline': None,
            'enable-local-file-access': None,
            'enable-javascript': True,  # Enable JavaScript execution
            'javascript-delay': 5000,    # Wait for 5 seconds for JavaScript to render
            'enable-smart-shrinking': True, # Improve rendering of complex layouts
            'no-stop-slow-scripts': True # Prevent wkhtmltopdf from stopping slow scripts
        }
        
        pdf_content = pdfkit.from_file(html_file_path, False, configuration=config, options=options)
        print("PDF content generated successfully.")

        return StreamingResponse(
            iter([pdf_content]),
            media_type="application/pdf",
            headers={"Content-Disposition": f"attachment; filename=\"report.pdf\""}
        )
    except HTTPException as http_exc:
        logging.error(f"HTTP Exception during PDF generation: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        logging.error(f"An unexpected error occurred during PDF generation: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"An unexpected server error occurred: {e}")
    finally:
        if html_file_path and os.path.exists(html_file_path):
            os.remove(html_file_path)
            print(f"Cleaned up temporary file: {html_file_path}")