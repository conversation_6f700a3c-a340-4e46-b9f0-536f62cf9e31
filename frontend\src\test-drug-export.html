<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تصدير تقارير الأدوية</title>
    <style>
        body {
            font-family: 'Arial', 'Traditional Arabic', 'Amiri', sans-serif;
            direction: rtl;
            text-align: right;
            margin: 20px;
            background: white;
        }
        .test-content {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            direction: rtl;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .summary-cards {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .summary-card {
            border: 1px solid #ddd;
            padding: 15px;
            min-width: 150px;
            text-align: center;
            background: #f9f9f9;
        }
        .buttons {
            text-align: center;
            margin: 30px 0;
        }
        button {
            padding: 10px 20px;
            margin: 10px;
            font-size: 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .pdf-btn { background: #28a745; color: white; }
        .excel-btn { background: #17a2b8; color: white; }
        .print-btn { background: #007bff; color: white; }
    </style>
</head>
<body>
    <div id="drug-report-content">
        <h1>تقرير صرف الأدوية - اختبار</h1>
        
        <!-- Summary Cards -->
        <div class="summary-cards">
            <div class="summary-card">
                <h3>إجمالي التكلفة</h3>
                <h2>16,125.00</h2>
            </div>
            <div class="summary-card">
                <h3>إجمالي الكمية</h3>
                <h2>11,700</h2>
            </div>
            <div class="summary-card">
                <h3>عدد الحالات</h3>
                <h2>365</h2>
            </div>
        </div>

        <!-- Clinic Report -->
        <div class="test-content">
            <h2>تقرير العيادات</h2>
            <table>
                <tr>
                    <th>اسم العيادة</th>
                    <th>اسم الدواء</th>
                    <th>الفئة</th>
                    <th>الوحدة</th>
                    <th>سعر الوحدة</th>
                    <th>الكمية الإجمالية</th>
                    <th>التكلفة الإجمالية</th>
                    <th>عدد الحالات</th>
                </tr>
                <tr>
                    <td>عيادة الرياض الرئيسية</td>
                    <td>أموكسيسيلين 500 مجم</td>
                    <td>مضادات حيوية</td>
                    <td>كبسولة</td>
                    <td>2.50</td>
                    <td>1,000</td>
                    <td>2,500.00</td>
                    <td>50</td>
                </tr>
                <tr>
                    <td>عيادة الرياض الرئيسية</td>
                    <td>باراسيتامول 500 مجم</td>
                    <td>مسكنات</td>
                    <td>قرص</td>
                    <td>0.75</td>
                    <td>2,000</td>
                    <td>1,500.00</td>
                    <td>100</td>
                </tr>
            </table>
        </div>

        <!-- Region Report -->
        <div class="test-content">
            <h2>تقرير المناطق</h2>
            <table>
                <tr>
                    <th>اسم المنطقة</th>
                    <th>اسم الدواء</th>
                    <th>الفئة</th>
                    <th>الوحدة</th>
                    <th>سعر الوحدة</th>
                    <th>الكمية الإجمالية</th>
                    <th>التكلفة الإجمالية</th>
                    <th>عدد الحالات</th>
                </tr>
                <tr>
                    <td>منطقة الرياض</td>
                    <td>فيتامين د 1000 وحدة</td>
                    <td>فيتامينات</td>
                    <td>كبسولة</td>
                    <td>1.25</td>
                    <td>1,500</td>
                    <td>1,875.00</td>
                    <td>75</td>
                </tr>
                <tr>
                    <td>منطقة الرياض</td>
                    <td>أسبرين 100 مجم</td>
                    <td>مضادات التجلط</td>
                    <td>قرص</td>
                    <td>0.50</td>
                    <td>3,000</td>
                    <td>1,500.00</td>
                    <td>120</td>
                </tr>
            </table>
        </div>

        <!-- Branch Report -->
        <div class="test-content">
            <h2>تقرير الفروع</h2>
            <table>
                <tr>
                    <th>اسم الفرع</th>
                    <th>اسم الدواء</th>
                    <th>الفئة</th>
                    <th>الوحدة</th>
                    <th>سعر الوحدة</th>
                    <th>الكمية الإجمالية</th>
                    <th>التكلفة الإجمالية</th>
                    <th>عدد الحالات</th>
                </tr>
                <tr>
                    <td>فرع الشمال</td>
                    <td>إنسولين طويل المفعول</td>
                    <td>أدوية السكري</td>
                    <td>قلم</td>
                    <td>45.00</td>
                    <td>200</td>
                    <td>9,000.00</td>
                    <td>40</td>
                </tr>
                <tr>
                    <td>فرع الشمال</td>
                    <td>ميتفورمين 500 مجم</td>
                    <td>أدوية السكري</td>
                    <td>قرص</td>
                    <td>0.25</td>
                    <td>5,000</td>
                    <td>1,250.00</td>
                    <td>80</td>
                </tr>
            </table>
        </div>
    </div>

    <div class="buttons">
        <button class="pdf-btn" onclick="testPdfExport()">اختبار تصدير PDF</button>
        <button class="excel-btn" onclick="testExcelExport()">اختبار تصدير Excel</button>
        <button class="print-btn" onclick="testPrint()">اختبار الطباعة</button>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <script>
        // Test PDF export
        async function testPdfExport() {
            const element = document.getElementById('drug-report-content');
            
            try {
                const canvas = await html2canvas(element, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    logging: false
                });

                const imgData = canvas.toDataURL('image/png', 1.0);
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF('p', 'mm', 'a4');
                
                const imgWidth = 210;
                const pageHeight = 297;
                const imgHeight = (canvas.height * imgWidth) / canvas.width;
                let heightLeft = imgHeight;
                let position = 0;

                pdf.setFontSize(16);
                pdf.text('تقرير صرف الأدوية', imgWidth / 2, 15, { align: 'center' });
                position = 25;

                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                heightLeft -= pageHeight - position;

                while (heightLeft >= 0) {
                    position = heightLeft - imgHeight;
                    pdf.addPage();
                    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                    heightLeft -= pageHeight;
                }

                pdf.save('تقرير-الأدوية-اختبار.pdf');
                alert('تم تصدير PDF بنجاح!');
            } catch (error) {
                console.error('خطأ في تصدير PDF:', error);
                alert('خطأ في تصدير PDF');
            }
        }

        // Test Excel export
        function testExcelExport() {
            try {
                const wb = XLSX.utils.book_new();
                
                // Clinic data
                const clinicData = [
                    ['اسم العيادة', 'اسم الدواء', 'الفئة', 'الوحدة', 'سعر الوحدة', 'الكمية الإجمالية', 'التكلفة الإجمالية', 'عدد الحالات'],
                    ['عيادة الرياض الرئيسية', 'أموكسيسيلين 500 مجم', 'مضادات حيوية', 'كبسولة', 2.50, 1000, 2500.00, 50],
                    ['عيادة الرياض الرئيسية', 'باراسيتامول 500 مجم', 'مسكنات', 'قرص', 0.75, 2000, 1500.00, 100]
                ];

                const clinicWs = XLSX.utils.aoa_to_sheet(clinicData);
                XLSX.utils.book_append_sheet(wb, clinicWs, 'تقرير العيادات');

                // Region data
                const regionData = [
                    ['اسم المنطقة', 'اسم الدواء', 'الفئة', 'الوحدة', 'سعر الوحدة', 'الكمية الإجمالية', 'التكلفة الإجمالية', 'عدد الحالات'],
                    ['منطقة الرياض', 'فيتامين د 1000 وحدة', 'فيتامينات', 'كبسولة', 1.25, 1500, 1875.00, 75],
                    ['منطقة الرياض', 'أسبرين 100 مجم', 'مضادات التجلط', 'قرص', 0.50, 3000, 1500.00, 120]
                ];

                const regionWs = XLSX.utils.aoa_to_sheet(regionData);
                XLSX.utils.book_append_sheet(wb, regionWs, 'تقرير المناطق');

                // Branch data
                const branchData = [
                    ['اسم الفرع', 'اسم الدواء', 'الفئة', 'الوحدة', 'سعر الوحدة', 'الكمية الإجمالية', 'التكلفة الإجمالية', 'عدد الحالات'],
                    ['فرع الشمال', 'إنسولين طويل المفعول', 'أدوية السكري', 'قلم', 45.00, 200, 9000.00, 40],
                    ['فرع الشمال', 'ميتفورمين 500 مجم', 'أدوية السكري', 'قرص', 0.25, 5000, 1250.00, 80]
                ];

                const branchWs = XLSX.utils.aoa_to_sheet(branchData);
                XLSX.utils.book_append_sheet(wb, branchWs, 'تقرير الفروع');

                XLSX.writeFile(wb, 'تقرير-الأدوية-اختبار.xlsx');
                alert('تم تصدير Excel بنجاح!');
            } catch (error) {
                console.error('خطأ في تصدير Excel:', error);
                alert('خطأ في تصدير Excel');
            }
        }

        // Test print
        function testPrint() {
            try {
                const element = document.getElementById('drug-report-content');
                const printWindow = window.open('', '_blank', 'width=800,height=600');
                
                if (!printWindow) {
                    alert('فشل في فتح نافذة الطباعة');
                    return;
                }

                const printDocument = `
                    <!DOCTYPE html>
                    <html lang="ar" dir="rtl">
                    <head>
                        <meta charset="UTF-8">
                        <title>تقرير صرف الأدوية</title>
                        <style>
                            body { font-family: Arial; direction: rtl; text-align: right; margin: 20px; }
                            table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                            th, td { border: 1px solid #333; padding: 8px; text-align: right; }
                            th { background-color: #f0f0f0; font-weight: bold; }
                            h1 { text-align: center; }
                            .summary-cards { display: flex; gap: 20px; margin: 20px 0; }
                            .summary-card { border: 1px solid #ddd; padding: 15px; text-align: center; }
                        </style>
                    </head>
                    <body>
                        ${element.innerHTML}
                        <div style="margin-top: 30px; text-align: center; font-size: 10pt; color: #666;">
                            تم طباعة هذا التقرير في: ${new Date().toLocaleDateString('ar-SA')} - ${new Date().toLocaleTimeString('ar-SA')}
                        </div>
                    </body>
                    </html>
                `;

                printWindow.document.write(printDocument);
                printWindow.document.close();

                printWindow.onload = () => {
                    setTimeout(() => {
                        printWindow.print();
                        printWindow.close();
                    }, 500);
                };

                alert('تم إرسال التقرير للطباعة!');
            } catch (error) {
                console.error('خطأ في الطباعة:', error);
                alert('خطأ في الطباعة');
            }
        }
    </script>
</body>
</html>
