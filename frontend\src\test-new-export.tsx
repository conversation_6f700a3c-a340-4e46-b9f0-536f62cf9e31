import React from 'react';
import { But<PERSON>, <PERSON>, Typography, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material';
import { exportComparisonReportToPdf, exportComparisonReportToExcel } from './utils/pdfExport';

// Test data
const testComparisonData = [
  {
    EntityName: 'عيادة الرياض',
    CategoryName: 'مضادات حيوية',
    DrugName: 'أموكسيسيلين',
    TotalQuantity: 1000,
    TotalCost: 25000.50,
    NumberOfCases: 50
  },
  {
    EntityName: 'عيادة جدة',
    CategoryName: 'مسكنات',
    DrugName: 'باراسيتامول',
    TotalQuantity: 2000,
    TotalCost: 15000.75,
    NumberOfCases: 100
  },
  {
    EntityName: 'عيادة الدمام',
    CategoryName: 'فيتامينات',
    DrugName: 'فيتامين د',
    TotalQuantity: 1500,
    TotalCost: 30000.25,
    NumberOfCases: 75
  }
];

const testTrendData = [
  {
    Month: '2024-01',
    TotalQuantity: 1200,
    TotalCost: 18000.00,
    NumberOfCases: 60
  },
  {
    Month: '2024-02',
    TotalQuantity: 1400,
    TotalCost: 21000.00,
    NumberOfCases: 70
  },
  {
    Month: '2024-03',
    TotalQuantity: 1800,
    TotalCost: 27000.00,
    NumberOfCases: 90
  }
];

const testKpiData = {
  totalCost: 70500.50,
  totalQuantity: 4500,
  totalCases: 225
};

const TestExportComponent: React.FC = () => {
  const handleTestPdfExport = async () => {
    try {
      await exportComparisonReportToPdf('test-content', 'تقرير-اختبار.pdf', 'تقرير مقارنة الأدوية - اختبار');
      alert('تم تصدير PDF بنجاح!');
    } catch (error) {
      console.error('خطأ في تصدير PDF:', error);
      alert('خطأ في تصدير PDF');
    }
  };

  const handleTestExcelExport = async () => {
    try {
      await exportComparisonReportToExcel(
        testComparisonData,
        testTrendData,
        testKpiData,
        'تقرير-اختبار.xlsx',
        'تقرير مقارنة الأدوية - اختبار'
      );
      alert('تم تصدير Excel بنجاح!');
    } catch (error) {
      console.error('خطأ في تصدير Excel:', error);
      alert('خطأ في تصدير Excel');
    }
  };

  return (
    <Box sx={{ p: 3, direction: 'rtl' }} id="test-content">
      <Typography variant="h4" gutterBottom align="center">
        تقرير مقارنة الأدوية - اختبار
      </Typography>

      {/* KPI Cards */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3, justifyContent: 'center' }}>
        <Paper sx={{ p: 2, minWidth: 150, textAlign: 'center' }}>
          <Typography variant="h6" color="primary">التكلفة الإجمالية</Typography>
          <Typography variant="h4">{testKpiData.totalCost.toLocaleString()}</Typography>
        </Paper>
        <Paper sx={{ p: 2, minWidth: 150, textAlign: 'center' }}>
          <Typography variant="h6" color="primary">الكمية الإجمالية</Typography>
          <Typography variant="h4">{testKpiData.totalQuantity.toLocaleString()}</Typography>
        </Paper>
        <Paper sx={{ p: 2, minWidth: 150, textAlign: 'center' }}>
          <Typography variant="h6" color="primary">عدد الحالات</Typography>
          <Typography variant="h4">{testKpiData.totalCases.toLocaleString()}</Typography>
        </Paper>
      </Box>

      {/* Comparison Data Table */}
      <Paper sx={{ mb: 3 }}>
        <Typography variant="h6" sx={{ p: 2 }}>بيانات المقارنة</Typography>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell align="right">اسم الكيان</TableCell>
                <TableCell align="right">الفئة</TableCell>
                <TableCell align="right">اسم الدواء</TableCell>
                <TableCell align="right">الكمية الإجمالية</TableCell>
                <TableCell align="right">التكلفة الإجمالية</TableCell>
                <TableCell align="right">عدد الحالات</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {testComparisonData.map((row, index) => (
                <TableRow key={index}>
                  <TableCell align="right">{row.EntityName}</TableCell>
                  <TableCell align="right">{row.CategoryName}</TableCell>
                  <TableCell align="right">{row.DrugName}</TableCell>
                  <TableCell align="right">{row.TotalQuantity.toLocaleString()}</TableCell>
                  <TableCell align="right">{row.TotalCost.toFixed(2)}</TableCell>
                  <TableCell align="right">{row.NumberOfCases}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Trend Data Table */}
      <Paper sx={{ mb: 3 }}>
        <Typography variant="h6" sx={{ p: 2 }}>الاتجاهات الشهرية</Typography>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell align="right">الشهر</TableCell>
                <TableCell align="right">الكمية الإجمالية</TableCell>
                <TableCell align="right">التكلفة الإجمالية</TableCell>
                <TableCell align="right">عدد الحالات</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {testTrendData.map((row, index) => (
                <TableRow key={index}>
                  <TableCell align="right">{row.Month}</TableCell>
                  <TableCell align="right">{row.TotalQuantity.toLocaleString()}</TableCell>
                  <TableCell align="right">{row.TotalCost.toFixed(2)}</TableCell>
                  <TableCell align="right">{row.NumberOfCases}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Export Buttons */}
      <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', mt: 3 }}>
        <Button 
          variant="contained" 
          color="success" 
          onClick={handleTestPdfExport}
          size="large"
        >
          اختبار تصدير PDF
        </Button>
        <Button 
          variant="contained" 
          color="info" 
          onClick={handleTestExcelExport}
          size="large"
        >
          اختبار تصدير Excel
        </Button>
      </Box>
    </Box>
  );
};

export default TestExportComponent;
